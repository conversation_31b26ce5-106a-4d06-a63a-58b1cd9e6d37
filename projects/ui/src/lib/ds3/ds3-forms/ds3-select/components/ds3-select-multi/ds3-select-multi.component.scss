@import "../../../../../../../assets/ds3/colors.var.scss";
@import "../../../../../../../assets/ds3/shadow/shadow.scss";
@import "../../../../../../../assets/ds3/fonts/fonts.scss";

:host {
	width: 100%;
}

.ds3-select {
	position: relative;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0px 8px 0px 1rem;
	z-index: 1;
	height: 40px;
}

.ds3-select-body {
	display: flex;
	align-items: center;
	position: relative;
	width: calc(100% - 32px);
	height: 40px;
	min-height: 24px;
	overflow: hidden;
	z-index: -1;
}

.ds3-select-value {
	position: absolute;
	margin-bottom: 0;
	color: $supportBlack03;

	.overflowing {
		visibility: hidden;
	}
}

.ds3-select-value-length {
	display: block;
}

.ds3-select-hidden-chipslist {
	position: absolute;
	height: 0;
	overflow: hidden;
}

.ds3-select-input {
	border: none;
	background-color: transparent;
	outline: none;
	padding: 0;
	color: $supportBlack03;
}

.ds3-select-multiple-options {
	width: 100%;
	max-height: 42vh;
	overflow-y: auto;
	background-color: $plane02;
	border-radius: 4px;
	padding: 16px 16px 0;
	border: 1px solid $supportGray03;

	::ng-deep {
		.ds3-option {
			display: flex;
			align-items: center;
			gap: 8px;
			@extend .pct-body2;
			padding: 8px 0;
			cursor: pointer;

			.pct {
				color: $actionDefaultAble04;
			}

			&:hover {
				background-color: transparent;
				color: $actionDefaultAble04;
			}
		}
	}

	ds3-option {
		&:first-child {
			::ng-deep {
				.ds3-option {
					padding-top: 0;
				}
			}
		}

		&:last-child {
			::ng-deep {
				.ds3-option {
					padding-bottom: 16px;
				}
			}
		}
	}
}

.ds3-select-multi-search-section {
	display: flex;
	gap: 8px;

	.ds3-select-refresh {
		display: flex;
		justify-content: flex-end;
		padding: 8px 0px;
	}

	.ds3-select-multiple-search {
		width: 100%;
		height: 40px;
		margin-bottom: 8px;
		padding-left: 16px;
		border-radius: 4px;
		border: 1px solid $supportGray03;
		color: $typeDefaultText;

		::placeholder {
			color: $typeDefaultText;
		}

		&:focus {
			border-color: $actionDefaultAble05;
			outline: none;
		}
	}
}

.ds3-select-multiple-title {
	display: flex;
	align-items: center;
	gap: 16px;
	margin: 16px 0;
	color: $typeDefaultTitle;
	cursor: pointer;

	&.ds3-select-multiple-title-disabled {
		cursor: not-allowed;

		.pct,
		h2 {
			color: $actionDefaultDisabled02;
		}
	}

	.pct {
		font-size: 16px;
	}

	h2 {
		display: flex;
		align-items: center;
		gap: 8px;
		margin-bottom: 0;
		@extend .pct-title5;
	}
}

.ds3-disabled {
	.ds3-select-value {
		color: $supportGray04;
	}

	ds3-select-arrow::ng-deep {
		.ds3-select-arrow {
			cursor: default;
			color: $supportGray04;
		}
	}
}

::ng-deep ds3-option.is-highlighted > .ds3-option {
	border: 1px solid var(--color-action-bgdark-able-2);
}
