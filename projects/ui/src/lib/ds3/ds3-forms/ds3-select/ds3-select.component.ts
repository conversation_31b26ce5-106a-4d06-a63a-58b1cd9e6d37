import { Overlay, OverlayConfig, OverlayRef } from "@angular/cdk/overlay";
import { CdkPortal } from "@angular/cdk/portal";
import { HttpClient } from "@angular/common/http";
import {
	AfterViewChecked,
	Component,
	ElementRef,
	EventEmitter,
	HostListener,
	Input,
	OnDestroy,
	OnInit,
	Output,
	ViewChild,
	forwardRef,
	OnChanges,
	SimpleChanges,
	ChangeDetectorRef,
} from "@angular/core";
import {
	ControlValueAccessor,
	FormControl,
	NG_VALUE_ACCESSOR,
} from "@angular/forms";
import { SnotifyService } from "ng-snotify";
import { Observable, Subscription, of } from "rxjs";
import { catchError, debounceTime, map } from "rxjs/operators";

export interface Ds3SelectOptionModel {
	value: string;
	name: string;
}
export type Ds3SelectFilterParamBuilder = (filter: string) => {
	[param: string]: string | string[];
};

export type Ds3SelectFilterResponseParser = (result: any) => any[];

@Component({
	selector: "ds3-select",
	templateUrl: "./ds3-select.component.html",
	styleUrls: ["./ds3-select.component.scss"],
	providers: [
		{
			provide: NG_VALUE_ACCESSOR,
			useExisting: forwardRef(() => Ds3SelectComponent),
			multi: true,
		},
	],
})
export class Ds3SelectComponent
	implements
		OnInit,
		AfterViewChecked,
		OnChanges,
		OnDestroy,
		ControlValueAccessor
{
	selectedValue: any = "";

	public isOpen: boolean = false;
	public searchControl = new FormControl();
	public tempOptions;

	public isLoading;

	public onChanged: Function;
	public onTouched: Function;

	private overlayRef!: OverlayRef;

	@ViewChild(CdkPortal, { static: true }) portal!: CdkPortal;

	@ViewChild("ds3Select", { static: true })
	selectElement: ElementRef;

	@ViewChild("ds3SelectSearch", { static: true })
	searchElement: ElementRef;

	@ViewChild("ds3SelectArrow", { static: true })
	SelectArrow: ElementRef;

	@Input()
	public placeholder?: string = "Selecione um item";

	@Input()
	public options: any[] = [];

	@Input()
	public valueKey: string = "value";

	@Input()
	public emptyMsg: string = "Nenhuma opção encontrada.";

	@Input()
	public nameKey: string = "name";

	@Input()
	public sortKey: string;

	@Input()
	public disabled: boolean;

	@Input()
	public remove: boolean = false;

	@Input()
	endpointUrl;

	@Input()
	paramBuilder: Ds3SelectFilterParamBuilder;

	@Input()
	responseParser: Ds3SelectFilterResponseParser;

	@Input()
	additionalFilters: any;

	@Input()
	initOption: any;

	@Input()
	addEmptyOption = false;

	@Input()
	useFullOption = false;

	@Output()
	valueChanges = new EventEmitter();

	@Output() opened: EventEmitter<any> = new EventEmitter<any>();
	id: string = "";

	@Output()
	searchEvent: EventEmitter<{ term: string }> = new EventEmitter();
	public highlightedIndex: number = -1;
	private searchSubscription: Subscription;

	private fetchDataCache = new Map<string, any>();
	private lastFetchTime: number = 0;
	private readonly CACHE_DURATION: number = 30000; // 30s
	private readonly TIME_TO_ENABLE_RELOAD: number = 30000;

	canReload: boolean = false;
	private timeToEnableReloadInterval;

	constructor(
		private http: HttpClient,
		private overlay: Overlay,
		private cd: ChangeDetectorRef,
		private elementRef: ElementRef,
		private notificationService: SnotifyService
	) {}

	ngOnInit() {
		this.id = this.elementRef.nativeElement.id;
		this.updateTempOptions();
		this.searchControl.valueChanges
			.pipe(debounceTime(300))
			.subscribe((term) => this.search(term));
		this.search(null, this.selectedValue);
		this._initReloadInterval();
	}

	ngOnChanges(changes: SimpleChanges) {
		if (changes.options && changes.options.currentValue) {
			const options = changes.options;
			if (!options.firstChange) {
				this.options = options.currentValue;
				this.updateTempOptions();
			}
		}
	}

	ngOnDestroy(): void {
		if (this.isOpen) {
			this.hide();
		}
		this._clearReloadInterval();
		this.fetchDataCache.clear();
	}

	private _resetReloadInterval(canReload) {
		this.canReload = canReload;
		this._clearReloadInterval();
		this._initReloadInterval();
	}

	private _initReloadInterval() {
		if (!this.endpointUrl) {
			return;
		}
		this.timeToEnableReloadInterval = setInterval(() => {
			if (this.canReload) {
				this._clearReloadInterval();
				return;
			}
			this.canReload = true;
			this.cd.markForCheck();
		}, this.TIME_TO_ENABLE_RELOAD);
	}

	private _clearReloadInterval() {
		clearInterval(this.timeToEnableReloadInterval);
		this.timeToEnableReloadInterval = null;
	}

	private updateTempOptions() {
		if (this.options) {
			this.tempOptions = [...this.options];
		}
	}

	toggleDropdown(): void {
		if (this.isOpen) {
			this.hide();
			return;
		}
		if (this.remove && this.selectedValue) {
			this.selectValue(null);
			return;
		}
		this.showDropdown();
		this.highlightedIndex = -1;
	}

	showDropdown(): void {
		if (this.disabled || this.isOpen) {
			return;
		}
		if (!this.overlayRef) {
			this.overlayRef = this.overlay.create(this.getOverlayConfig());
		}
		this.overlayRef.attach(this.portal);
		this.syncWidth();
		this.isOpen = true;
		this.opened.emit(this.isOpen);
	}

	private hide(): void {
		this.overlayRef.detach();
		this.isOpen = false;
		this.opened.emit(this.isOpen);
	}

	@HostListener("document:click", ["$event", "$event.target"])
	clickedOutside(e: MouseEvent, targetElement: HTMLElement) {
		if (!this.isOpen) {
			return;
		}

		if (!this.isDescendant(this.selectElement.nativeElement, targetElement)) {
			this.hide();
		}
	}

	ngAfterViewChecked() {
		if (this.isOpen) {
			this.searchElement.nativeElement.focus();
		}
	}

	private getOverlayConfig(): OverlayConfig {
		const positionStrategy = this.overlay
			.position()
			.flexibleConnectedTo(this.selectElement.nativeElement)
			.withPush(true)
			.withPositions([
				{
					originX: "start",
					originY: "bottom",
					overlayX: "start",
					overlayY: "top",
					offsetY: 4,
				},
				{
					originX: "end",
					originY: "bottom",
					overlayX: "end",
					overlayY: "top",
					offsetY: 4,
				},
				{
					originX: "start",
					originY: "top",
					overlayX: "start",
					overlayY: "bottom",
					offsetY: -4,
				},
				{
					originX: "end",
					originY: "top",
					overlayX: "end",
					overlayY: "bottom",
					offsetY: -4,
				},
			]);

		const scrollStrategy = this.overlay.scrollStrategies.reposition();
		return new OverlayConfig({
			positionStrategy: positionStrategy,
			scrollStrategy: scrollStrategy,
			hasBackdrop: false,
			backdropClass: "cdk-overlay-transparent-backdrop",
		});
	}

	private syncWidth(): void {
		if (!this.overlayRef) {
			return;
		}
		const refRectWidth =
			this.selectElement.nativeElement.getBoundingClientRect().width;
		this.overlayRef.updateSize({ width: refRectWidth });
	}

	search(term, initValue = null, reload = false) {
		if (this.endpointUrl) {
			this.isLoading = true;
			this.searchSubscription = this.fetchData(term, reload).subscribe(
				(result) => {
					this.isLoading = false;
					this.options = result;

					if (this.initOption && this.options && Array.isArray(this.options)) {
						const existOption = this.options.some(
							(option) =>
								option[this.valueKey] === this.initOption[this.valueKey]
						);
						if (!existOption) {
							this.options.push(this.initOption);
						}
					}

					if (this.addEmptyOption) {
						const emptyOption: any = {};
						emptyOption[this.valueKey] = "";
						emptyOption[this.nameKey] = "-";
						this.options.unshift(emptyOption);
					}
					if (initValue) {
						let valueToSelect = initValue;
						if (initValue instanceof Object) {
							valueToSelect = initValue[this.valueKey];
						}
						this.selectValue(
							this.options.find(
								(option) => option[this.valueKey] === valueToSelect
							)
						);
					}
					this.cd.detectChanges();
				}
			);
			return;
		}

		if (this.tempOptions) {
			const temp = this.tempOptions.filter((x) => {
				const lowercaseTerm = term ? term.toLowerCase() : term;

				if (
					x[this.nameKey].toLowerCase().indexOf(lowercaseTerm) !== -1 ||
					!lowercaseTerm
				) {
					return x;
				}
			});
			this.options = temp;
		}

		if (!this.endpointUrl && (!this.options || this.options.length === 0)) {
			this.searchEvent.emit({ term });
		}
		this.highlightedIndex = -1;
	}

	private isDescendant(parentElement, childElement) {
		let node = childElement.parentNode;
		if (parentElement === childElement) {
			return true;
		} else {
			while (node !== null) {
				if (node === parentElement) {
					return true;
				}
				node = node.parentNode;
			}
			return false;
		}
	}

	private fetchData(term: string, reload: boolean): Observable<any> {
		let cacheKey = `${this.id}`;
		if (this.additionalFilters && this.additionalFilters.length > 0) {
			cacheKey += `-${JSON.stringify(this.additionalFilters)}`;
		}
		const now = Date.now();

		const currentDataCache = this.fetchDataCache.get(cacheKey);
		const lowercaseTerm = term ? term.toLowerCase() : term;

		if (reload && now - this.lastFetchTime < this.TIME_TO_ENABLE_RELOAD) {
			this.notificationService.warning(
				`Houve uma atualização nos últimos ${
					this.TIME_TO_ENABLE_RELOAD / 1000
				}s, aguarde um momento para atualizar novamente!`
			);
			this._resetReloadInterval(false);
			return of(currentDataCache || []);
		}

		if (
			currentDataCache &&
			now - this.lastFetchTime < this.CACHE_DURATION &&
			!reload
		) {
			if (this.searchSubscription) {
				this.searchSubscription.unsubscribe();
			}
			let filtered = currentDataCache.filter((x) => {
				if (
					x[this.nameKey].toLowerCase().indexOf(lowercaseTerm) !== -1 ||
					!lowercaseTerm ||
					lowercaseTerm.length === 0
				) {
					return x;
				}
			});
			if (filtered.length !== 0) {
				filtered = this._sortValues(filtered);
				return of(filtered);
			}
		}
		const url = this.endpointUrl;
		const params = this.paramBuilder ? this.paramBuilder(term) : {};

		if (this.additionalFilters) {
			const filters = JSON.parse(params.filters.toString());
			Object.keys(this.additionalFilters).forEach((key) => {
				filters[key] = this.additionalFilters[key];
			});
			params.filters = JSON.stringify(filters);
		}

		const data = this.http.get(url, { params });

		return data.pipe(
			map((result) => {
				this._resetReloadInterval(false);
				const parsedResult: any = this.responseParser
					? this.responseParser(result)
					: result;
				const resultContent = parsedResult.content
					? parsedResult.content
					: parsedResult;
				// Cache the result
				if (this.fetchDataCache.has(cacheKey)) {
					let actualResult = this.fetchDataCache.get(cacheKey);
					const actualResultContent = actualResult.map((v) => v[this.valueKey]);

					resultContent.forEach((rc) => {
						if (!actualResultContent.includes(rc[this.valueKey])) {
							actualResult.push(rc);
						}
					});

					actualResult = this._sortValues(actualResult);

					this.fetchDataCache.set(cacheKey, actualResult);
				} else {
					this.fetchDataCache.set(cacheKey, resultContent);
				}
				this.lastFetchTime = now;
				return resultContent;
			}),
			catchError(() => {
				if (this.responseParser) {
					return of(this.responseParser([]));
				} else {
					return of([]);
				}
			})
		);
	}

	private _sortValues(data) {
		const sortKey = this.sortKey ? this.sortKey : this.nameKey;
		return data.sort((a, b) => {
			const aValue = a[sortKey] ? a[sortKey] : "";
			const bValue = b[sortKey] ? b[sortKey] : "";
			if (aValue > bValue) {
				return 1;
			} else if (aValue < bValue) {
				return -1;
			} else {
				return 0;
			}
		});
	}

	selectValue(value: any): void {
		if (value && value.disabled) {
			return;
		}
		this.writeValue(value);
		this.onChanged(this.selectedValue);
		this.hide();
		this.onTouched();
		this.valueChanges.emit(value);
		if (
			this.SelectArrow &&
			this.SelectArrow.nativeElement &&
			this.SelectArrow.nativeElement.firstChild &&
			this.SelectArrow.nativeElement.firstChild.firstChild
		) {
			this.SelectArrow.nativeElement.firstChild.firstChild.focus();
		}
	}

	registerOnChange(angularProvidedFunction: any): void {
		this.onChanged = angularProvidedFunction;
	}

	registerOnTouched(angularProvidedFunction: any): void {
		this.onTouched = angularProvidedFunction;
	}

	setDisabledState(isDisabled: boolean): void {
		this.disabled = isDisabled;
	}

	writeValue(value: any): void {
		if (value === undefined || value === "") {
			return;
		}
		if (this.useFullOption) {
			const foundOption = this.options.find((x) => x[this.valueKey] === value);
			this.selectedValue = foundOption;
			return;
		}
		this.selectedValue = value;
	}

	isValueSelected(value: any) {
		return this.selectedValue == value;
	}

	get valueSearch(): boolean {
		return (
			this.searchControl &&
			this.searchControl.value &&
			this.searchControl.value !== ""
		);
	}

	@HostListener("keydown", ["$event"])
	onKeyDown(event: KeyboardEvent) {
		if (!this.isOpen) return;

		switch (event.key) {
			case "ArrowDown":
				if (!!this.options.length || !!this.tempOptions.length) {
					event.preventDefault();
					this.highlightedIndex =
						(this.highlightedIndex + 1) % this.options.length;
					this.scrollToHighlighted();
				}
				break;
			case "ArrowUp":
				if (!!this.options.length || !!this.tempOptions.length) {
					event.preventDefault();
					this.highlightedIndex =
						(this.highlightedIndex - 1 + this.options.length) %
						this.options.length;
				}
				this.scrollToHighlighted();
				break;
			case "Enter":
				event.preventDefault();
				if (this.highlightedIndex >= 0 && this.options[this.highlightedIndex]) {
					this.selectValue(this.options[this.highlightedIndex][this.valueKey]);
				}
				break;
			case "Escape":
				event.preventDefault();
				this.hide();
				break;
		}
		this.highlightedIndex =
			this.highlightedIndex === null ? -1 : this.highlightedIndex;
	}

	private scrollToHighlighted() {
		setTimeout(() => {
			const optionElements = document.querySelectorAll(
				".ds3-select-options ds3-option"
			);
			if (optionElements[this.highlightedIndex]) {
				(optionElements[this.highlightedIndex] as HTMLElement).scrollIntoView({
					block: "nearest",
				});
			}
		}, 0);
	}
}
