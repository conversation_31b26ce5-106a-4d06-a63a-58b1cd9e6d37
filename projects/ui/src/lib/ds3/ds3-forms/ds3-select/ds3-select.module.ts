import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { Ds3ButtonModule } from "../../ds3-button/ds3-button.module";
import { Ds3TooltipModule } from "../../ds3-tooltip/ds3-tooltip.module";
import { Ds3SelectComponent } from "./ds3-select.component";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { Ds3OptionComponent } from "./components/ds3-option/ds3-option.component";
import { Ds3SelectFindOptionNameBasedOnValuePipe } from "./pipes/ds3-select-find-option-name-based-on-value.pipe";
import { Ds3DiviserModule } from "../../ds3-diviser/ds3-diviser.module";
import { Ds3ChipsModule } from "../../ds3-chips/ds3-chips.module";
import { Ds3SelectMultiComponent } from "./components/ds3-select-multi/ds3-select-multi.component";
import { Ds3SelectArrowComponent } from "./components/ds3-select-arrow/ds3-select-arrow.component";
import { PortalModule } from "@angular/cdk/portal";
import { OverlayModule } from "@angular/cdk/overlay";
import { A11yModule } from "@angular/cdk/a11y";

@NgModule({
	declarations: [
		Ds3SelectComponent,
		Ds3OptionComponent,
		Ds3SelectFindOptionNameBasedOnValuePipe,
		Ds3SelectMultiComponent,
		Ds3SelectArrowComponent,
	],
	imports: [
		CommonModule,
		FormsModule,
		ReactiveFormsModule,
		Ds3DiviserModule,
		Ds3ChipsModule,
		PortalModule,
		OverlayModule,
		Ds3ButtonModule,
		Ds3TooltipModule,
		A11yModule,
	],
	exports: [
		Ds3SelectComponent,
		Ds3OptionComponent,
		Ds3SelectMultiComponent,
		Ds3SelectArrowComponent,
	],
})
export class Ds3SelectModule {}
