@import "../../../../../assets/ds3/colors.var.scss";
@import "../../../../../assets/ds3/shadow/shadow.scss";
@import "../../../../../assets/ds3/fonts/fonts.scss";

:host {
	width: 100%;
}

.ds3-select {
	position: relative;
	display: flex;
	align-items: center;
	justify-content: flex-end;
	padding: 0px 8px 0px 1rem;
	z-index: 0;
	height: 40px;
}

.ds3-select-body {
	position: absolute;
	display: flex;
	align-items: center;
	width: calc(100% - 32px);
	height: 40px;
	min-height: 24px;
	overflow: hidden;
	z-index: -1;
}

.ds3-select-value {
	position: absolute;
	margin-bottom: 0;
	color: $supportBlack03;
	text-transform: capitalize;

	.ds3-select-value-placeholder {
		text-transform: none;
	}

	.overflowing {
		visibility: hidden;
	}
}

.ds3-select-value-length {
	display: block;
	margin-top: 24px;
}

.ds3-select-input {
	border: none;
	background-color: transparent;
	outline: none;
	padding: 0;
	color: $supportBlack03;
}

.ds3-select-options {
	width: 100%;
	max-height: 42vh;
	overflow-y: auto;
	border: 1px solid var(--color-support-gray-3);
	background-color: $plane02;
	border-radius: 4px;
	@extend .sh-bg-desktop2;

	::ng-deep {
		.ds3-option {
			display: block;
			@extend .pct-body1-regular;
			padding: 10px 16px;
			cursor: pointer;

			&:hover {
				background-color: $actionDefaultAble01;
			}
		}
	}

	.is-selected {
		::ng-deep {
			.ds3-option {
				background-color: $actionDefaultAble01;
			}
		}
	}

	.is-disabled {
		&:hover {
			cursor: not-allowed;
			pointer-events: none;
		}

		::ng-deep.ds3-option {
			background-color: $actionDefaultDisabled01;
			color: $actionDefaultDisabled02;
		}
	}

	.is-highlighted {
		::ng-deep.ds3-option {
			border: 3px solid var(--color-support-gray-2);
		}
	}
}

.ds3-select-options-empty {
	@extend .pct-body1;
	margin-bottom: 0;
	padding: 8px 16px;
}

.ds3-disabled {
	.ds3-select-value {
		color: $supportGray04;
	}

	ds3-select-arrow::ng-deep {
		.ds3-select-arrow {
			cursor: default;
			color: $supportGray04;
		}
	}
}

.ds3-select-refresh {
	display: flex;
	justify-content: flex-end;
	padding: 2px 16px;
	width: 100%;
}
