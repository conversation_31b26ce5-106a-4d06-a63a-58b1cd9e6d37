import {
	AfterViewInit,
	Component,
	ElementRef,
	HostListener,
	Inject,
	Input,
	OnDestroy,
	OnInit,
	Renderer2,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { DOCUMENT } from "@angular/common";

let uniqueId = 0;

@Component({
	selector: "[ds3Tooltip]",
	templateUrl: "./ds3-tooltip.component.html",
	styleUrls: ["./ds3-tooltip.component.scss"],
})
export class Ds3TooltipComponent implements OnInit, AfterViewInit, OnDestroy {
	@Input() ds3Tooltip: TemplateRef<any> | string | false;
	@Input() showTooltipButton: boolean = false;
	@Input() tooltipIndicator:
		| "left"
		| "right"
		| "top-center"
		| "top-left"
		| "top-right"
		| "bottom-center"
		| "bottom-left"
		| "bottom-right" = "bottom-center";
	@Input() tooltipPosition: "left" | "right" | "top" | "bottom" = "top";
	@Input() multiline: boolean = false;
	@Input() scrollableContainerName: string = "main";
	@Input() aditionalPosition: number = 0;
	@Input() asInnerHtml: boolean;
	@ViewChild("tooltip", { static: false }) tooltip: ElementRef;

	overlay: HTMLElement;

	render2Listeners: Array<() => any> = new Array<() => any>();

	constructor(
		private render2: Renderer2,
		private elementRef: ElementRef,
		@Inject(DOCUMENT) private document
	) {}

	ngOnInit() {
		if (!this.ds3Tooltip) {
			return;
		}
		this.render2.setStyle(
			this.elementRef.nativeElement,
			"position",
			"relative"
		);
		this.createOverlay();
	}

	ngOnDestroy() {
		this.removeChildTooltip();
		this.render2Listeners.forEach((i) => {
			i();
		});
	}

	ngAfterViewInit() {
		if (!this.elementRef.nativeElement.id) {
			this.render2.setAttribute(
				this.elementRef.nativeElement,
				"id",
				`tooltip-trigger-${uniqueId++}`
			);
		}
		if (!this.tooltip.nativeElement.id) {
			this.render2.setAttribute(
				this.tooltip.nativeElement,
				"id",
				`tooltip-${this.elementRef.nativeElement.id}`
			);
		}
		const listenerEnter = this.render2.listen(
			this.elementRef.nativeElement,
			"mouseenter",
			() => {
				if (this.showTooltipButton) {
					return;
				}

				if (this.ds3Tooltip == false) {
					return;
				}
				this.overlay.appendChild(this.tooltip.nativeElement);
				this.tooltip.nativeElement.style.visibility = "visible";
				this.tooltip.nativeElement.style.display = "block";
				this.calculateTooltipPosition();

				if (this.multiline) {
					const numberOfLines =
						this.tooltip.nativeElement.scrollHeight /
						this.tooltip.nativeElement.clientHeight;
					if (numberOfLines > 4) {
						return;
					}
					this.tooltip.nativeElement.style.width = "328px";
				}
			}
		);

		const listenerClick = this.render2.listen(
			this.elementRef.nativeElement,
			"click",
			(event) => {
				if (!this.showTooltipButton) {
					return;
				}

				event.stopPropagation();

				if (this.tooltip.nativeElement.style.visibility === "visible") {
					this.tooltip.nativeElement.style.visibility = "hidden";
					this.tooltip.nativeElement.style.display = "none";
					this.removeChildTooltip();
				} else {
					this.overlay.appendChild(this.tooltip.nativeElement);
					this.tooltip.nativeElement.style.visibility = "visible";
					this.tooltip.nativeElement.style.display = "block";
					this.calculateTooltipPosition();
				}
			}
		);

		const listenerLeave = this.render2.listen(
			this.elementRef.nativeElement,
			"mouseleave",
			() => {
				if (this.showTooltipButton) {
					return;
				}

				this.tooltip.nativeElement.style.visibility = "hidden";
				this.tooltip.nativeElement.style.display = "none";
				this.removeChildTooltip();
			}
		);

		this.render2Listeners.push(listenerEnter, listenerClick, listenerLeave);

		const scrollContainerElement = document
			.getElementsByTagName(this.scrollableContainerName)
			.item(0);

		if (scrollContainerElement) {
			const scrollableContainerScroll = this.render2.listen(
				document.getElementsByTagName(this.scrollableContainerName).item(0),
				"scroll",
				() => {
					if (this.showTooltipButton) {
						return;
					}

					this.tooltip.nativeElement.style.visibility = "hidden";
					this.tooltip.nativeElement.style.display = "none";
					this.removeChildTooltip();
				}
			);

			this.render2Listeners.push(scrollableContainerScroll);
		}
	}

	@HostListener("click", ["$event"])
	hostClickHandler($event) {
		this.clickHandler($event);
	}

	@HostListener("document:click", ["$event"])
	public clickHandler($event) {
		if (this.tooltip) {
			const innerClick = this.isDescendant(
				this.tooltip.nativeElement,
				$event.target
			);
			if (!innerClick) {
				this.tooltip.nativeElement.style.visibility = "hidden";
				this.tooltip.nativeElement.style.display = "none";
				this.removeChildTooltip();
			}
		}
	}

	private removeChildTooltip() {
		if (this.overlay) {
			if (this.overlay.children.length === 0) {
				return;
			}
			const children = Array.from(this.overlay.children);
			for (const child of children) {
				if (child.id.includes(this.tooltip.nativeElement.id)) {
					this.overlay.removeChild(child);
					break;
				}
			}
		}
	}

	private isDescendant(parentElement, childElement) {
		let node = childElement.parentNode;
		if (parentElement === childElement) {
			return true;
		} else {
			while (node !== null) {
				if (node === parentElement) {
					return true;
				}
				node = node.parentNode;
			}
			return false;
		}
	}

	private calculateTooltipPosition() {
		const triggerRect: ClientRect | DOMRect =
			this.elementRef.nativeElement.getBoundingClientRect();
		const tooltipRect: ClientRect | DOMRect =
			this.tooltip.nativeElement.getBoundingClientRect();
		if (this.tooltipPosition === "top") {
			this.tooltip.nativeElement.style.top =
				triggerRect.top +
				this.aditionalPosition -
				tooltipRect.height -
				10 +
				"px";
			this.tooltip.nativeElement.style.left =
				triggerRect.left + triggerRect.width / 2 - tooltipRect.width / 2 + "px";
		} else if (this.tooltipPosition === "bottom") {
			this.tooltip.nativeElement.style.top =
				triggerRect.top +
				this.aditionalPosition +
				triggerRect.height +
				10 +
				"px";
			this.tooltip.nativeElement.style.left =
				triggerRect.left + triggerRect.width / 2 - tooltipRect.width / 2 + "px";
		} else if (this.tooltipPosition === "left") {
			this.tooltip.nativeElement.style.left =
				triggerRect.left +
				this.aditionalPosition -
				tooltipRect.width -
				10 +
				"px";
			this.tooltip.nativeElement.style.top =
				triggerRect.top +
				triggerRect.height / 2 -
				tooltipRect.height / 2 +
				"px";
		} else if (this.tooltipPosition === "right") {
			this.tooltip.nativeElement.style.left =
				triggerRect.right + this.aditionalPosition + 10 + "px";
			this.tooltip.nativeElement.style.top =
				triggerRect.top +
				triggerRect.height / 2 -
				tooltipRect.height / 2 +
				"px";
		}
	}

	createOverlay() {
		this.overlay = this.document.getElementById("pacto-cat-overlay");
		if (!this.overlay) {
			this.overlay = this.document.createElement("div");
			this.overlay.id = "pacto-cat-overlay";
			this.overlay.style.position = "absolute";
			this.overlay.style.top = "0px";
			this.overlay.style.bottom = "0px";
			this.overlay.style.width = "100%";
			this.overlay.style.pointerEvents = "none";
			this.overlay = document.body.appendChild(this.overlay);
		}
	}

	public isTemplate(): boolean {
		return (
			typeof this.ds3Tooltip !== "string" &&
			typeof this.ds3Tooltip !== undefined
		);
	}

	hideTooltip() {
		this.showTooltipButton = false;
	}

	toggleVariation() {
		this.showTooltipButton = !this.showTooltipButton;
	}
}
