import {
	Component,
	OnInit,
	Input,
	ChangeDetectionStrategy,
	ViewChild,
	ElementRef,
	HostListener,
	ChangeDetectorRef,
	On<PERSON>estroy,
} from "@angular/core";
import { trigger, style, transition, animate } from "@angular/animations";
import { HttpClient } from "@angular/common/http";
import { FormControl } from "@angular/forms";
import { SnotifyService } from "ng-snotify";

import { debounceTime, map, catchError } from "rxjs/operators";
import { Subscription, Observable, of } from "rxjs";
import { PactoUtilService } from "../../pacto-util.service";
import {
	SelectFilterParamBuilder,
	SelectFilterResponseParser,
} from "../cat-select-filter/cat-select-filter.component";

@Component({
	selector: "pacto-cat-multi-select-filter",
	templateUrl: "./cat-multi-select-filter.component.html",
	styleUrls: ["./cat-multi-select-filter.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
	animations: [
		trigger("inOut", [
			transition("void => *", [
				animate(
					0,
					style({
						transform: "translateX(-25px) scale(1)",
						opacity: 0,
					})
				),
				animate(
					"100ms 0ms ease-in-out",
					style({
						transform: "translateX(0px) scale(1)",
						opacity: 1,
					})
				),
			]),
			transition("* => void", [
				animate(
					"100ms 0ms ease-in-out",
					style({
						transform: "translateX(25px) scale(1)",
						opacity: 0,
					})
				),
			]),
		]),
	],
})
export class CatMultiSelectFilterComponent implements OnInit, OnDestroy {
	@Input() id;
	/**
	 * Full url of endpoint without any GET params.
	 *
	 */
	@Input() endpointUrl;
	@Input() options: any[] = [];
	@Input() control: FormControl;
	@Input() paramBuilder: SelectFilterParamBuilder;
	@Input() resposeParser: SelectFilterResponseParser;
	@Input() placeholder = "-";
	@Input() label;
	@Input() idKey = "id";
	@Input() labelKey = "label";

	@Input()
	public sortKey: string;

	@Input() imageKey;
	@Input() disabled = false;
	@Input() autocomplete = "on";

	@ViewChild("currentElRef", { static: true }) currentElRef: ElementRef;
	@ViewChild("selectArea", { static: true }) selectArea: ElementRef;
	@ViewChild("filter", { static: true }) filter;

	private searchSubscription: Subscription;

	filterFC: FormControl = new FormControl("");
	optionsOpen = false;
	loading = false;

	private fetchDataCache = new Map<string, any>();
	private lastFetchTime: number = 0;
	private readonly CACHE_DURATION: number = 30000; // 30s
	private readonly TIME_TO_ENABLE_RELOAD: number = 30000;

	canReload: boolean = false;
	private timeToEnableReloadInterval;

	constructor(
		private compRef: ElementRef,
		private http: HttpClient,
		private cd: ChangeDetectorRef,
		private util: PactoUtilService,
		private notificationService: SnotifyService
	) {}

	get isInvalid() {
		return this.control && this.control.touched && this.control.invalid;
	}

	get showError() {
		return this.isInvalid && !this.disabled;
	}

	get currentOptions(): any[] {
		if (this.control && this.control.value && this.control.value.length) {
			return this.control.value;
		} else {
			return [];
		}
	}

	get nofOptions() {
		return this.currentOptions.length;
	}

	get filteredOptions() {
		/**
		 * In case of server-side filtering
		 */
		if (this.endpointUrl) {
			const options = Object.assign([], this.options);
			return options.filter((item) => {
				return !this.isSelected(item);
			});

			/**
			 * In case of pre-loaded options
			 */
		} else {
			const options = Object.assign([], this.options);

			return options
				.filter((item) => {
					return this.itemMatchesFilter(item);
				})
				.filter((item) => {
					return !this.isSelected(item);
				});
		}
	}

	private itemMatchesFilter(item): boolean {
		const token = item[this.labelKey];
		let filter = this.filterFC.value;
		filter = filter ? filter : "";
		const regex = new RegExp(`${filter}`, "gi");
		return regex.test(token);
	}

	private isSelected(item): boolean {
		const id = item[this.idKey];
		return this.currentOptions.find((itemI) => {
			return `${itemI[this.idKey]}` === `${id}`;
		});
	}

	ngOnInit() {
		if (!this.control) {
			this.control = new FormControl();
		}
		if (!this.options) {
			this.options = [];
		}
		this.validateId();
		this.control.registerOnDisabledChange((disabled) => {
			if (disabled) {
				this.optionsOpen = false;
			}
		});

		/**
		 * Cancel result callback
		 */
		this.filterFC.valueChanges.subscribe(() => {
			if (this.searchSubscription) {
				this.searchSubscription.unsubscribe();
			}
		});

		/**
		 * Trigger search after delay
		 */
		this.filterFC.valueChanges.pipe(debounceTime(500)).subscribe((term) => {
			this.search(term);
		});
		this.control.valueChanges.subscribe(() => {
			this.cd.detectChanges();
		});
		this._initReloadInterval();
	}

	ngOnDestroy() {
		this._clearReloadInterval();
		this.fetchDataCache.clear();
	}

	removeOptionHandler(index, $event: MouseEvent) {
		if (this.control.enabled) {
			$event.preventDefault();
			$event.stopPropagation();

			if (this.currentOptions[index]) {
				const current: any[] = this.control.value;
				current.splice(index, 1);
				this.control.setValue(current);
			}
		}
	}

	pillClickHandler($event: MouseEvent) {
		$event.preventDefault();
		$event.stopPropagation();
	}

	currentClickHandler($event: MouseEvent) {
		if (this.isSelectAreaClick($event) && this.control.enabled) {
			if (!this.optionsOpen) {
				this.open();
			} else {
				this.optionsOpen = false;
			}
		}
	}

	clearAllHandler($event: MouseEvent) {
		if (this.control.enabled) {
			$event.preventDefault();
			$event.stopPropagation();
			this.control.setValue([]);
		}
	}

	selectOptionHandler(option, $event: MouseEvent) {
		$event.preventDefault();
		$event.stopPropagation();
		this.setValue(option);
	}

	@HostListener("document:click", ["$event"])
	clickDocumentHandler($event) {
		if (!this.isComponentClick($event)) {
			this.optionsOpen = false;
		}
	}

	private setValue(value) {
		const current = this.control.value;
		if (current && current.length) {
			current.push(value);
			this.control.setValue(current);
		} else {
			this.control.setValue([value]);
		}
	}

	private open() {
		this.filterFC.setValue("", { emitEvent: false });
		this.search(null);
		this.optionsOpen = true;
		setTimeout(() => {
			this.filter.nativeElement.focus();
		});
	}

	search(term, reload = false) {
		if (this.endpointUrl) {
			this.loading = true;
			this.cd.detectChanges();
			this.searchSubscription = this.fetchData(term, reload).subscribe(
				(result) => {
					this.loading = false;
					this.options = result;
					this.cd.detectChanges();
				}
			);
		}
	}

	private fetchData(term: string, reload = false): Observable<any> {
		const cacheKey = `${this.id}`;
		const now = Date.now();

		const currentDataCache = this.fetchDataCache.get(cacheKey);
		const lowercaseTerm = term ? term.toLowerCase() : term;

		if (reload && now - this.lastFetchTime < this.TIME_TO_ENABLE_RELOAD) {
			this.notificationService.warning(
				`Houve uma atualização nos últimos ${
					this.TIME_TO_ENABLE_RELOAD / 1000
				}s, aguarde um momento para atualizar novamente!`
			);
			this._resetReloadInterval(false);
			return of(currentDataCache || []);
		}

		if (
			currentDataCache &&
			now - this.lastFetchTime < this.CACHE_DURATION &&
			!reload
		) {
			if (this.searchSubscription) {
				this.searchSubscription.unsubscribe();
			}
			let filtered = currentDataCache.filter((x) => {
				if (
					x[this.labelKey].toLowerCase().indexOf(lowercaseTerm) !== -1 ||
					!lowercaseTerm ||
					lowercaseTerm.length === 0
				) {
					return x;
				}
			});
			if (filtered.length !== 0) {
				filtered = this._sortValues(filtered);
				return of(filtered);
			}
		}
		const url = this.endpointUrl;
		const params = this.paramBuilder ? this.paramBuilder(term) : {};
		const data = this.http.get(url, { params });

		return data.pipe(
			map((result) => {
				this._resetReloadInterval(false);
				const parsedResult: any = this.resposeParser
					? this.resposeParser(result)
					: result;
				const resultContent = parsedResult.content
					? parsedResult.content
					: parsedResult;
				// Cache the result
				if (this.fetchDataCache.has(cacheKey)) {
					let actualResult = this.fetchDataCache.get(cacheKey);
					const actualResultContent = actualResult.map((v) => v[this.labelKey]);

					resultContent.forEach((rc) => {
						if (!actualResultContent.includes(rc[this.labelKey])) {
							actualResult.push(rc);
						}
					});

					actualResult = this._sortValues(actualResult);

					this.fetchDataCache.set(cacheKey, actualResult);
				} else {
					this.fetchDataCache.set(cacheKey, resultContent);
				}
				this.lastFetchTime = now;
				return resultContent;
			}),
			catchError(() => {
				if (this.resposeParser) {
					return of(this.resposeParser([]));
				} else {
					return of([]);
				}
			})
		);
	}

	private validateId() {
		if (!this.id) {
			this.id = "cat-select-filter-id-" + Math.trunc(Math.random() * 1000);
		}
	}

	private isSelectAreaClick($event: MouseEvent) {
		return this.isComponentClick($event) && !this.isOptionAreaClick($event);
	}

	private isComponentClick($event: MouseEvent) {
		return this.util.isDescendant(
			this.compRef.nativeElement,
			$event.target as Element
		);
	}

	private isOptionAreaClick($event: MouseEvent) {
		return this.util.isDescendant(
			this.selectArea.nativeElement,
			$event.target as Element
		);
	}

	private _resetReloadInterval(canReload) {
		this.canReload = canReload;
		this._clearReloadInterval();
		this._initReloadInterval();
	}

	private _initReloadInterval() {
		if (!this.endpointUrl) {
			return;
		}
		this.timeToEnableReloadInterval = setInterval(() => {
			if (this.canReload) {
				this._clearReloadInterval();
				return;
			}
			this.canReload = true;
			this.cd.markForCheck();
		}, this.TIME_TO_ENABLE_RELOAD);
	}

	private _clearReloadInterval() {
		clearInterval(this.timeToEnableReloadInterval);
		this.timeToEnableReloadInterval = null;
	}

	private _sortValues(data) {
		const sortKey = this.sortKey ? this.sortKey : this.labelKey;
		return data.sort((a, b) => {
			const aValue = a[sortKey] ? a[sortKey] : "";
			const bValue = b[sortKey] ? b[sortKey] : "";
			if (aValue > bValue) {
				return 1;
			} else if (aValue < bValue) {
				return -1;
			} else {
				return 0;
			}
		});
	}
}
