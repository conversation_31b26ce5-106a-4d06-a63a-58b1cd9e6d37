@import "projects/ui/assets/import.scss";
@import "projects/ui/assets/ds3/fonts/fonts.scss";

:host {
	display: block;
	width: 100%;
}

.pacto-label {
	@extend .type-h6;
	color: $gelo04;
	line-height: 28px;

	&.small {
		@extend .type-p-small;
		color: $gelo04;
		line-height: 22px;
	}
}

input.filter-input {
	width: 100%;
	border-radius: 3px;
	border: 1px solid $gelo03;
	@extend .type-p-small-rounded;
	padding: 0px 30px 0px 10px;
	line-height: 40px;
	color: $gelo05;
	outline: 0px !important;

	&:focus {
		box-shadow: 0 0 0 0.2rem rgba($azulimPri, 0.5);
	}

	&:disabled {
		border-color: $cinzaClaroPri !important;
		background-color: $cinzaClaroPri !important;
	}
}

input.cat-select-filter:disabled + .current-value {
	border-color: $cinzaClaroPri;
	background-color: $cinzaClaroPri;
	cursor: not-allowed;
}

.empty-state {
	@extend .type-p-small;
	color: $cinzaClaro05;
	padding-left: 5px;
}

.current-value.small {
	line-height: 30px;

	.double-arrow {
		bottom: 7px;
	}

	.options {
		top: 33px;
	}

	.clear-icon {
		bottom: 5px;
	}
}

.current-value {
	position: relative;
	cursor: pointer;
	@extend .type-p-small-rounded;
	background-color: $branco;
	white-space: nowrap;
	color: $pretoPri;
	line-height: 40px;
	padding: 0px 10px;
	border: 1px solid $gelo03;
	border-radius: 6px;

	&.disabled {
		cursor: not-allowed;
		border-color: $cinzaClaroPri;
		background-color: $cinzaClaroPri;

		.clear-icon {
			cursor: not-allowed;
		}
	}

	&.error {
		border-color: $hellboyPri;
	}

	&.opened {
		border-color: $azulimPri;
	}
}

.current-value.disabled .current-wrapper {
	opacity: 0.6;
}

.current-wrapper {
	display: flex;
	align-items: center;

	pacto-cat-person-avatar {
		margin-right: 10px;
	}

	.option-label {
		width: calc(100% - 13px);
		overflow: hidden;
		text-overflow: ellipsis;
	}

	.double-arrow {
		position: absolute;
		right: 13px;
		bottom: 13px;
	}

	.clear-icon {
		position: absolute;
		cursor: pointer;
		right: 10px;
		bottom: 10px;
		font-size: 20px;
		color: $gelo04;
	}
}

.scroll-container {
	padding: 5px 0px;
	border-bottom: 1px solid $cinzaClaroPri;
}

.options {
	position: absolute;
	z-index: 10;
	box-shadow: 0 1px 2px 1px #c0c5d0;
	border-bottom-left-radius: 6px;
	border-bottom-right-radius: 6px;
	padding: 10px;
	top: 41px;
	left: 2px;
	right: 2px;
	background-color: $branco;

	.option-container {
		display: flex;
		line-height: 34px;

		pacto-cat-person-avatar {
			margin-right: 15px;
			align-content: center;
		}
	}

	.option-sub-label {
		@extend .pct-overline2-regular;
	}

	.option-sub-label-tipo {
		@extend .pct-overline2-bold;
	}

	.option {
		font-family: "Nunito Sans", sans-serif;
		font-size: 14px;
		font-weight: bold;
		color: $gelo04;
		line-height: 1.5em;
		padding: 7.5px;
		white-space: normal;

		&:hover {
			background-color: $cinza01;
		}
	}
}

.option-label-container {
	display: grid;
}

.option-sub-label-container {
	line-height: normal;
}

.options.small {
	.option-container {
		line-height: 24px;
	}

	.option {
		line-height: 24px;
		padding: 5px;
	}
}

.cat-select-refresh {
	margin-left: 5px;
}
