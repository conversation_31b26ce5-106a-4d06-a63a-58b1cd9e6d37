<input class="cat-select-filter" type="hidden" [formControl]="control" />
<div class="pct-error-msg" *ngIf="error">
	<span>{{ errorMsg }}</span>
</div>
<div
	class="current-value"
	id="{{ id }}"
	[ngClass]="{
		opened: optionsOpen,
		error: error,
		disabled: control?.disabled,
		small: small
	}"
	(click)="currentClickHandler()"
	cdkOverlayOrigin
	#origin="cdkOverlayOrigin">
	<!-- CURRENT SELECTION -->
	<div class="current-wrapper">
		<pacto-cat-person-avatar
			*ngIf="currentOption && imageKey"
			[uri]="currentOption[imageKey]"
			[diameter]="small ? 24 : 34"></pacto-cat-person-avatar>
		<span class="option-label">
			{{ currentOption ? getOptionLabel(currentOption) : placeholder }}
		</span>
		<i class="arrow pct pct-chevron-down" #arrow></i>
		<i
			(click)="clearHandler($event)"
			*ngIf="currentOption && hasClearAction"
			class="clear-icon pct pct-x"></i>
	</div>
</div>

<ng-template
	cdkConnectedOverlay
	[cdkConnectedOverlayOrigin]="origin"
	[cdkConnectedOverlayOpen]="optionsOpen">
	<!-- LIST OF OPTIONS -->
	<div
		class="options"
		[ngClass]="{ small: small }"
		[style.width]="
			origin.elementRef.nativeElement.getBoundingClientRect().width + 'px'
		">
		<div class="d-flex align-items-center">
			<input
				class="filter-input"
				#filter
				id="{{ this.id + '-filter' }}"
				(click)="$event.stopPropagation()"
				[formControl]="filterFC"
				*ngIf="showFilter"
				[placeholder]="'form.filter' | translate" />
			<div
				class="cat-select-refresh"
				*ngIf="canReload"
				(click)="$event.stopPropagation()">
				<div
					ds3Tooltip="Atualizar as opções"
					(click)="$event.stopPropagation()">
					<button
						ds3-icon-button
						[id]="id + '-refresh-option'"
						(click)="
							search(filterFC.value, null, true); $event.stopPropagation()
						">
						<i class="pct pct-refresh-cw"></i>
					</button>
				</div>
			</div>
		</div>

		<div
			class="add-btn"
			*ngIf="showAddBtn"
			(click)="add(); selectOptionHandler()">
			<i class="pct pct-edit"></i>
			&nbsp;ADICIONAR NOVO
		</div>
		<div
			class="scroll-container"
			*ngIf="infiniteScrollEnabled"
			infiniteScroll
			[infiniteScrollDistance]="2"
			[infiniteScrollThrottle]="50"
			[scrollWindow]="false"
			(scrolled)="onDropdownScrolled($event)"
			[alwaysCallback]="true">
			<ng-container [ngTemplateOutlet]="optionsContainer"></ng-container>
		</div>

		<div class="scroll-container" *ngIf="!infiniteScrollEnabled">
			<ng-container [ngTemplateOutlet]="optionsContainer"></ng-container>
		</div>
	</div>
</ng-template>

<ng-template #optionsContainer>
	<div
		class="option"
		id="{{ this.id + '-' + index }}"
		*ngFor="let option of filteredOptions; let index = index; let last = last"
		[ds3Tooltip]="
			option
				| columnSelectOptionLabel
					: labelFn
					: options
					: idKey
					: labelKey
					: control
					: placeholder
		"
		tooltipPosition="right"
		tooltipIndicator="left"
		[aditionalPosition]="14"
		(click)="selectOptionHandler(option)">
		<div class="option-container" *ngIf="!selectItem?.templateRef">
			<pacto-cat-person-avatar
				*ngIf="imageKey"
				[uri]="option[imageKey]"
				[diameter]="small ? 24 : 34"></pacto-cat-person-avatar>
			<span class="option-label">
				{{
					option
						| columnSelectOptionLabel
							: labelFn
							: options
							: idKey
							: labelKey
							: control
							: placeholder
				}}
			</span>
		</div>

		<div *ngIf="selectItem?.templateRef">
			<ng-template
				*ngTemplateOutlet="
					selectItem.templateRef;
					context: { item: option }
				"></ng-template>
		</div>
	</div>

	<div
		class="empty-state"
		*ngIf="showEmptyMessage && !loading && !options.length">
		Nenhum resultado.
	</div>
</ng-template>
