@import "projects/ui/assets/import.scss";

:host {
	display: block;
	width: 100%;
}

.pacto-label {
	@extend .type-h6;
	color: $gelo04;
	line-height: 28px;

	&.small {
		@extend .type-p-small;
		color: $gelo04;
		line-height: 22px;
	}
}

input.filter-input {
	width: 100%;
	border-radius: 3px;
	border: 1px solid $gelo03;
	@extend .type-p-small-rounded;
	padding: 0px 30px 0px 10px;
	line-height: 40px;
	color: $gelo05;
	outline: 0px !important;

	&:focus {
		box-shadow: 0 0 0 0.2rem rgba($azulimPri, 0.5);
	}

	&:disabled {
		border-color: $cinzaClaroPri !important;
		background-color: $cinzaClaroPri !important;
	}
}

input.cat-select-filter:disabled + .current-value {
	border-color: $cinzaClaroPri;
	background-color: $cinzaClaroPri;
	cursor: not-allowed;
}

.empty-state {
	@extend .type-p-small;
	color: $cinzaClaro05;
	padding-left: 5px;
}

.current-value.small {
	line-height: 30px;

	.double-arrow {
		bottom: 7px;
	}

	.options {
		top: 33px;
	}

	.clear-icon {
		bottom: 5px;
	}
}

.current-value {
	position: relative;
	cursor: pointer;
	@extend .type-p-small-rounded;
	background-color: $branco;
	white-space: nowrap;
	color: $pretoPri;
	line-height: 40px;
	padding: 0px 10px;
	border: 1px solid $gelo03;
	border-radius: 6px;

	.option-label {
		width: calc(100% - 13px);
		overflow: hidden;
		text-overflow: ellipsis;
	}

	&.disabled {
		cursor: not-allowed;
		border-color: $cinzaClaroPri;
		background-color: $cinzaClaroPri;

		.clear-icon {
			cursor: not-allowed;
		}
	}

	&.error {
		border-color: $hellboyPri;
	}

	&.dropdown-toggle::after {
		display: none;
	}

	&.opened {
		border-color: $azulimPri;
	}
}

.current-value.disabled .current-wrapper {
	opacity: 0.6;
}

.current-wrapper {
	display: flex;
	align-items: center;

	pacto-cat-person-avatar {
		margin-right: 10px;
	}

	.double-arrow {
		position: absolute;
		right: 13px;
		bottom: 13px;
	}

	.clear-icon {
		position: absolute;
		cursor: pointer;
		right: 10px;
		bottom: 10px;
		font-size: 20px;
		color: $gelo04;
	}
}

.scroll-container {
	max-height: 245px;
	overflow: auto;
	padding: 5px 0px;
	border-bottom: 1px solid $cinzaClaroPri;

	&::-webkit-scrollbar {
		width: 6px;
		background: rgba(222, 222, 222, 0.75);
	}

	&::-webkit-scrollbar-track {
		border-radius: 10px;
	}

	&::-webkit-scrollbar-thumb {
		border-radius: 10px;
		background: rgba(0, 0, 0, 0.5);
	}
}

.options {
	box-shadow: 0 1px 2px 1px #c0c5d0;
	border-radius: 6px;
	padding: 10px;
	background-color: $branco;

	.option-container {
		display: flex;
		line-height: 34px;

		pacto-cat-person-avatar {
			margin-right: 15px;
		}
	}

	.option {
		font-family: "Nunito Sans", sans-serif;
		font-size: 14px;
		font-weight: bold;
		color: $gelo04;
		line-height: 1.5em;
		padding: 7.5px;
		white-space: normal;
		cursor: pointer;

		&:hover {
			background-color: $cinza01;
		}
	}
}

.options.small {
	.option-container {
		line-height: 24px;
	}

	.option {
		line-height: 24px;
		padding: 5px;
		font-weight: 600;
	}
}

.arrow {
	position: absolute;
	right: 8px;
	bottom: 8px;
	height: 24px;
	width: 24px;
	font-size: 24px;
}

.arrow.open {
	transform: rotate(180deg);
}

.add-btn {
	background: #eff2f7;
	border-radius: 4px;
	height: 40px;
	font-size: 0.8rem;
	padding-left: 9px;
	padding-right: 9px;
	padding-top: 13px;
	color: #90949a;
	cursor: pointer;

	.pct-edit {
		color: $azulimPri;
	}
}

// IE 9 only
@media all and (min-width: 0) and (min-resolution: 0.001dpcm) {
	.select {
		select {
			padding-right: 0;
		}

		&:after,
		&:before {
			display: none;
		}
	}
}

.pct-error-msg {
	@extend .type-caption;
	color: $hellboyPri;
	margin-top: 5px;
	line-height: 2em;
	min-height: 24px;
	padding-left: 5px;
}

.option-label {
	white-space: nowrap;
	overflow: auto;

	&::-webkit-scrollbar {
		height: 1px;
		width: 0;
		background: rgba(222, 222, 222, 0.75);
	}

	&::-webkit-scrollbar-track {
		border-radius: 10px;
	}

	&::-webkit-scrollbar-thumb {
		border-radius: 10px;
		background: rgba(0, 0, 0, 0.5);
	}
}

::ng-deep .cdk-overlay-container {
	z-index: 20000;
}

.cat-select-refresh {
	margin-left: 5px;
}
