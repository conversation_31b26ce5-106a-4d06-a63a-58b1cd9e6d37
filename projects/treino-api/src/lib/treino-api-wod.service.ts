import { Injectable } from "@angular/core";
import {
	ApiResponseList,
	ApiResponseSingle,
	ApiRequestQueries,
} from "./base.model";
import { Wod, WodBase, WodCreateEdit } from "./wod.model";
import { TreinoApiModule } from "./treino-api.module";

import { Observable } from "rxjs";
import { catchError, map } from "rxjs/operators";
import { TreinoApiBaseService } from "./treino-api-base.service";

@Injectable({
	providedIn: TreinoApiModule,
})
export class TreinoApiWodService {
	constructor(private restService: TreinoApiBaseService) {}

	obterListaWod(
		empresaId?: number,
		queries?: ApiRequestQueries,
		filtro?: string
	): Observable<ApiResponseList<WodBase>> {
		const params = queries ? queries.getParamsObject() : {};
		if (filtro) {
			params["filtroNome"] = filtro;
		}
		return this.restService.get("wods", { params });
	}

	obterWodsDia(dia): Observable<Array<WodBase>> {
		return this.restService
			.get(`wods/all`, {
				params: { data: dia },
			})
			.pipe(
				map((result: ApiResponseList<WodBase>) => {
					return result.content;
				})
			);
	}

	obterWod(id): Observable<Wod> {
		return this.restService.get(`wods/${id}`).pipe(
			map((response: ApiResponseSingle<Wod>) => {
				return response.content;
			})
		);
	}

	obterWodImportado(filtroDia: string): Observable<ApiResponseSingle<Wod>> {
		const params = {};
		if (filtroDia) {
			params["filtroDia"] = filtroDia;
		}
		return this.restService
			.get("wods/wods-importados-crossfit", { params })
			.pipe(
				map((response: ApiResponseSingle<Wod>) => {
					return response;
				}),
				catchError((error) => {
					return new Observable((observer) => {
						observer.error(error);
						observer.complete();
					});
				})
			);
	}

	atualizarWod(id, dados: WodCreateEdit): Observable<Wod> {
		return this.restService.put(`wods/${id}`, dados).pipe(
			map((response: ApiResponseSingle<Wod>) => {
				return response.content;
			})
		);
	}

	criarWod(dados: WodCreateEdit): Observable<any> {
		return this.restService.post(`wods`, dados).pipe(
			map((response: ApiResponseSingle<Wod>) => {
				return response.content;
			}),
			catchError((error) => {
				return new Observable((observer) => {
					observer.error(error);
					observer.complete();
				});
			})
		);
	}

	importarWodFranqueadora(): Observable<any> {
		return this.restService
			.post(`wods/importar-wods-da-franqueadora`, null)
			.pipe(
				map((response: ApiResponseSingle<Wod>) => {
					return response.content;
				}),
				catchError((error) => {
					return new Observable((observer) => {
						observer.error(error);
						observer.complete();
					});
				})
			);
	}

	removerWod(id: string): Observable<boolean> {
		return this.restService.delete(`wods/${id}`).pipe(
			map((response: ApiResponseSingle<boolean>) => {
				return response.content;
			})
		);
	}
}
