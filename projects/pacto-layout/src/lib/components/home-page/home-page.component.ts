import {
	ChangeDetectorRef,
	Component,
	ElementRef,
	EventEmitter,
	Inject,
	Input,
	OnChanges,
	OnInit,
	Optional,
	Output,
	ViewChild,
} from "@angular/core";
// import { AvisosModalComponent } from './components/avisos-modal/avisos-modal.component';
import { Router } from "@angular/router";
import { DOCUMENT } from "@angular/common";
import { forkJoin, isObservable, of } from "rxjs";
import { AdmMsApiFavoritoService } from "adm-ms-api";
import { LayoutNavigationService } from "../../navigation/layout-navigation.service";
import { TaskbarService } from "../../navigation/taskbar/taskbar.service";
import { PactoLayoutSearchService } from "../../navigation/pacto-layout-search/pacto-layout-search.service";
import { ToastrService } from "ngx-toastr";
import { AdmCoreApiAvisosService } from "adm-core-api";
import { PactoLayoutSDKWrapper } from "../../sdk-wrapper/sdk-wrappers";
import { NotificacoesService } from "../menu-topbar-actions/notificacoes/notificacoes.service";
import { AvisosModalComponent } from "./components/avisos-modal/avisos-modal.component";
import { DialogService, LoaderService, PactoModalSize } from "ui-kit";
import { FavoritesMenuConfigService } from "../../navigation/menu/menu-config/favorites/favorites-menu-config.service";
import { PermissaoService } from "../../navigation/permissao/permissao.service";
import { AvisosComponent } from "./components/avisos/avisos.component";
import { SessionService } from "sdk";
import { PlanoInativoService } from "../../services/plano-inativo.service";
import { PlanoInativoComponent } from "../plano-inativo/plano-inativo.component";
import { MatDialog } from "@angular/material/dialog";
import { catchError, switchMap } from "rxjs/operators";
interface Avisos {
	dataPublicacao: string;
	dataPublicacaoLong: number;
	id: number;
	link: string;
	modulo: string;
	texto: string;
	titulo: string;
}

@Component({
	selector: "pacto-home-page",
	templateUrl: "./home-page.component.html",
	styleUrls: ["./home-page.component.scss"],
})
export class HomePageComponent implements OnInit {
	fixados;
	menuTrocarUnidade: any = true;
	globalMenu;
	zona;
	@ViewChild("avisosComponent", { static: false })
	avisosComponent: AvisosComponent;
	avisosData;
	novidadesContent;
	permissaoEditarAviso;
	optionsColaboradores;
	optionsPerfis;

	constructor(
		private cd: ChangeDetectorRef,
		private router: Router,
		@Inject(DOCUMENT) private document,
		private admMsApiFavoritoService: AdmMsApiFavoritoService,
		private layoutNavigationService: LayoutNavigationService,
		private taskbar: TaskbarService,
		private toastService: ToastrService,
		private avisosService: AdmCoreApiAvisosService,
		@Optional() private pactoLayoutSDKWrapper: PactoLayoutSDKWrapper,
		private notificacoesService: NotificacoesService,
		private taskbarService: TaskbarService,
		private pactoLayoutSearchService: PactoLayoutSearchService,
		private dialogService: DialogService,
		private favoritesMenuConfigService: FavoritesMenuConfigService,
		private loaderService: LoaderService,
		private permissaoService: PermissaoService,
		private planoInativoService: PlanoInativoService,
		private sessionService: SessionService,
		private dialog: MatDialog
	) {}

	ngOnInit() {
		this.permissaoEditarAviso = this.permissaoService.temPermissaoAdm("2.86");
		this.loadFixados();
		this.admMsApiFavoritoService.favItensSubject$.subscribe((v) => {
			if (v != "update_home") {
				this.loadFixados();
			}
		});
		const usuarioId = this.sessionService.loggedUser.id;
		if (usuarioId && this.permissaoService.temPermissaoAdm("5.09")) {
			this.planoInativoService
				.deveExibirModalPlanosHoje(usuarioId)
				.pipe(
					switchMap((deveExibir) =>
						deveExibir
							? this.planoInativoService.consultarPlanosProximosInativar()
							: of(null)
					),
					catchError((error) => {
						console.error("Erro ao verificar modal de planos inativos:", error);
						return of(null);
					})
				)
				.subscribe((response) => {
					const temDados =
						response && response.content && response.content.length > 0;
					if (temDados) {
						this.dialog.open(PlanoInativoComponent, {
							disableClose: true,
							autoFocus: false,
							data: response,
						});
					}
				});
		}
	}

	get nomePessoa() {
		return this.pactoLayoutSDKWrapper.sessionService.loggedUser.username;
	}

	get nomeUnidade() {
		return this.pactoLayoutSDKWrapper.sessionService.currentEmpresa.nome;
	}

	get isMultiUnidade(): boolean {
		return !!document.getElementById("menu-trocar-unidade");
	}

	mudarUnidade() {
		document.getElementById("menu-trocar-unidade").click();
	}

	loadFixados() {
		this.loaderService.show();
		this.favoritesMenuConfigService.menus.subscribe((menuResponse) => {
			this.fixados = menuResponse.filter((item) => item.favInfo.tipo === 1);
			this.cd.detectChanges();
		});
		this.loaderService.hide();
	}

	desfixar(item) {
		this.admMsApiFavoritoService.removeFavorite(item.favInfo.codigo).subscribe(
			(response) => {
				this.toastService.success("Marcador removido dos favoritos");
				// this.taskbarService.updateSubject(
				// 	PlataformModuleConfig.FAVORITES
				// );
				this.loadFixados();
			},
			(error) => {
				this.toastService.error("Erro ao remover marcador dos favoritos");
			},
			() => {
				this.loadFixados();
			}
		);
		this.admMsApiFavoritoService._FavItensSubject.next("reload");
	}

	abrirMenuFixados() {
		this.document.getElementById("topbar-modules-toggle").click();
	}

	checkZona() {
		try {
			if (
				this.pactoLayoutSDKWrapper &&
				this.pactoLayoutSDKWrapper.serviceUrls() &&
				this.pactoLayoutSDKWrapper.serviceUrls().zwUrl
			) {
				const regex = /zw(\d+)\./;
				const match = this.pactoLayoutSDKWrapper
					.serviceUrls()
					.zwUrl.match(regex);
				if (match && match[1]) {
					return match[1];
				}
			}
			return "-1";
		} catch (e) {
			return "-1";
		}
	}

	loadNotificacoes() {
		this.zona = this.checkZona();

		this.notificacoesService
			.getVersaosContent(null, null, null, "todos", this.zona)
			.subscribe((response) => {
				this.novidadesContent = [
					response["content"][0],
					response["content"][1],
				];
				this.cd.detectChanges();
			});
	}

	redirectTo(item) {
		if (item.route.internalLink) {
			this.router.navigateByUrl(item.route.internalLink);
		} else if (isObservable(item.route.externalLink)) {
			item.route.externalLink.subscribe((response) => {
				const parameterCaracter = response.url.includes("?") ? "&" : "?";
				const urlPopup = response.url + parameterCaracter + "from=popup";
				const win = window.open(
					urlPopup,
					item.route.queryParams.windowTitle || "_self",
					response.popupInfo.features
				);
			});
		} else {
			window.open(item.route.externalLink, "_self");
		}
	}

	abrirNovidades() {
		document.getElementById("topbar-notificacoes").click();
		setTimeout(() => {
			document.getElementById("tab-undefined-2").click();
		}, 200);
	}

	openNovoAviso() {
		forkJoin([
			this.avisosService.listarColaboradores(),
			this.avisosService.listarPerfisAcesso(),
		]).subscribe(([colaboradores, perfis]) => {
			this.optionsColaboradores = colaboradores;
			this.optionsPerfis = perfis;

			const modal = this.dialogService.open(
				"Publicar novo aviso",
				AvisosModalComponent,
				PactoModalSize.LARGE,
				"modal-novo-aviso"
			);
			modal.componentInstance.optionsColaboradores = this.optionsColaboradores;
			modal.componentInstance.optionsPerfis = this.optionsPerfis;
			modal.componentInstance.avisoUpdated.subscribe((result: any) => {
				result.autor = {
					codigo: this.pactoLayoutSDKWrapper.sessionService.loggedUser.id,
				};
				this.avisosService
					.salvarOuAtualizarAviso(
						this.pactoLayoutSDKWrapper.sessionService.empresaId,
						result
					)
					.subscribe(
						(response) => {
							this.avisosComponent.loadAvisos();
							this.toastService.success("Aviso salvo com sucesso.");
						},
						(error) => {
							this.toastService.error("Erro ao salvar aviso");
						},
						() => {
							this.cd.detectChanges();
						}
					);
			});
		});
	}

	protected readonly name = name;
}
