import { Empresa } from "./empresa.model";
import { MovParcela } from "./mov-parcela.model";

export class Cliente {
	codigo: number;
	codigoMatricula: number;
	matricula: string;
	pessoa: Pessoa;
	situacao: string;
}

export class Pessoa {
	codigo: number;
	nome: string;
	fotoKey?: string;
	urlFoto?: string;
}

export class ClienteDadosPessoais {
	codigoCliente: number;
	codigoPessoa: number;
	idade: number;
	nascimento: number;
	sexo: string;
	empresa: Empresa;
	nome: string;
	situacao: string;
	situacaoContrato: string;
	matricula: string;
	urlFoto: string;
	emails: string[];
	telefones: any[];
	parqPositivo: boolean;
	dataMatricula: number;
	gympass?: boolean;
	totalpass?: boolean;
	gogood?: boolean;
	freepass?: boolean;
	objecao: string;
	cpf?: string;
	rne?: string;
	estrangeira?: boolean;
	cnpj?: string;
	sugestaoGPT: string;
	riscoChurn: number;
	riscoChurnLancamento: string;
	sesc: boolean;
	nomeSocial: string;
	matriculaSesc: string;
	renda: number;
	dataValidadeCarteirinha: number;
	categoria: string;
	possuiIdVindi: boolean;
	idVindi: number;
	empresaFornecedor: number;
}

export class ClienteDadosPlano {
	dataCadastro: number;
	dataMatricula: number;
	dataRematricula: number;
	avisos: number;
	observacoes: number;
	nomePlano: string;
	situacao: string;
	vinculos: Vinculo[];
	gympass?: boolean;
	totalpass?: boolean;
	inicioVinculoAtual?: Date;
	titularContratoNome?: string;
	titularContratoMatricula?: string;
	valorReceitaCliente?: number;
	valorReceitaMediaCliente?: number;
	atualizarBv?: boolean;
}

export class ClienteDadosAuxiliares {
	dataInclusaoSpc: number;
	parcelasSpc?: MovParcela[];
}

export class ClienteTotalPass {
	existe: boolean;
}

export class Vinculo {
	codigo: number;
	colaborador: string;
	tipoVinculo: string;
	codigoColaborador: number;
}

export class SenhaAcesso {
	matricula: string;
	senhaAcesso: string;
	confirmaSenhaAcesso: string;
	habilitaSenhaAcesso: boolean;
}

export interface DadosInicialAcessoManual {
	listaEmpresas: { codigo: number; nome: string }[];
	listaLocalDeAcesso: {
		codigo: number;
		descricao: string;
		listaColetor: ListaColetorDadosIniciais;
	}[];
	cliente: Cliente;
	dataDeAcesso: string;
	dataSaida: string;
	horaEntradaRegistroAcesso: string;
	horaSaidaRegistroAcesso: string;
	meioIdentificacaoEntrada: any;
	meioIdentificacaoSaida: any;
	dataRegistro: any;
	senhaAcesso: any;
	confirmaSenhaAcesso: any;
	habilitaSenhaAcesso: boolean;
	registrarSaida: boolean;
}

export interface ListaColetorDadosIniciais {
	codigo: number;
	descricao: string;
}

export interface LocalDeAcesso {
	codigo: number;
	descricao: string;
	listaColetor: ListaColetorDadosIniciais;
}

export interface DadosColetorAcessoManual {
	codigo: number;
	descricao: string;
}

export interface RegistarAcessoManual {
	matricula: number;
	sentido: string;
	situacao: string;
	dataDeAcesso: string;
	dataSaida: string;
	horaEntradaRegistroAcesso: string;
	horaSaidaRegistroAcesso: string;
	meioIdentificacaoEntrada: any;
	dataRegistro: any;
	registrarSaida: boolean;
	localAcesso: number;
	coletor: number;
}

export interface ClienteDadosTotalPass {
	empresa: number;
	cpf: string;
	pessoa: number;
	matricula: number;
}

export interface LinhaDoTempoDia {
	data: number;
	itens: LinhaDoTempo[];
}

export interface LinhaDoTempo {
	codigo: number;
	data: number;
	mensagem: string;
	operacaoDescricao: string;
	tipo: number;
	operacao: number;
	origem: string;
	usuario: string;
	observacao: string;
}

export class ClienteRestricao {
	nome: string;
	codigoMatricula: number;
	cpf: string;
	observacao: string;
	codigoEmpresa: number;
	nomeEmpresa: string;
	chaveEmpresa: string;
	tipo: string;
}

export enum TipoClienteRestricaoEnum {
	INADINPLENCIA = "IN",
	RESGRISTRO_MANUAL_TELA_CLIENTE = "RM",
}
