<pacto-cat-tabs-vertical>
	<ng-template
		i18n-label="@@adm:tab-label-ferias"
		label="Férias"
		pactoTabVertical="ferias">
		<div class="table-wrapper pacto-shadow">
			<pacto-cat-table-editable
				(confirm)="confirmFerias($event)"
				[isEditable]="true"
				[showDelete]="false"
				[table]="tableFerias"
				class="table-ferias"
				idSuffix="ferias"></pacto-cat-table-editable>
		</div>
	</ng-template>
	<ng-template
		i18n-label="@@adm:tab-label-data-pagamento"
		label="Data de pagamento"
		pactoTabVertical="data-pagamento">
		<pacto-cat-form-multi-select-filter
			[control]="form.get('diasVencimentoProrata')"
			[options]="days"
			i18n-label="adm:label-dias-prorata"
			id="select-dias-prorata"
			label="Escolha os dias do mês"></pacto-cat-form-multi-select-filter>
		<div>
			<pacto-cat-checkbox
				[control]="form.get('prorataObrigatorio')"
				i18n-label="adm:label-prorata-obrigatorio"
				id="check-prorata-obrigatorio"
				label="Data de vencimento obrigátoria"></pacto-cat-checkbox>
		</div>
		<div>
			<pacto-cat-checkbox
				[control]="form.get('obrigatorioInformarCartaoCreditoVenda')"
				i18n-label="adm:label-obrigatorio-informar-cartao-venda"
				id="check-obrigatorio-informar-cartao-venda"
				label="Obrigatoriedade de cadastro de cartão de crédito para venda"></pacto-cat-checkbox>
		</div>
	</ng-template>
	<ng-template
		i18n-label="@@adm:tab-label-renovacao-automatica"
		label="Renovação automática"
		pactoTabVertical="renovacao-automatica">
		<pacto-cat-checkbox
			[control]="form.get('renovavelAutomaticamente')"
			class="mt-16 mb-16 display-block"
			i18n-label="adm:label-renovavel-automaticamente"
			id="check-renovavel-automaticamente"
			label="Ativar renovação automática"></pacto-cat-checkbox>
		<pacto-cat-checkbox
			[control]="form.get('renovarAutomaticamenteComDesconto')"
			class="mt-16 mb-16 display-block"
			i18n-label="adm:label-renovar-plano-desconto"
			id="check-renovar-plano-desconto"
			label="Renovar plano com desconto"></pacto-cat-checkbox>
		<pacto-cat-checkbox
			[control]="form.get('renovarProdutoObrigatorio')"
			class="mt-16 mb-16 display-block"
			i18n-label="adm:label-renovar-produto-obrigatorio"
			id="check-renovar-produto-obrigatorio"
			label="Renovar produtos obrigatórios"></pacto-cat-checkbox>
		<pacto-cat-checkbox
			[control]="form.get('naoRenovarContratoParcelaVencidaAberto')"
			class="mt-16 mb-16 display-block"
			i18n-label="adm:label-nao-renovar-contrato-parcela-vencida-aberta"
			id="check-nao-renovar-contrato-parcela-vencida-aberta"
			label="Não renovar contrato com parcela vencida em aberto"></pacto-cat-checkbox>
	</ng-template>
	<ng-template
		i18n-label="@@adm:tab-label-parcela-pela-operado"
		label="Parcela pela operadora"
		pactoTabVertical="parcela-pela-operadora">
		<ng-container>
			<pacto-cat-checkbox
				[control]="form.get('parcelamentoOperadora')"
				class="mt-16 mb-16 display-block"
				i18n-label="@@adm:label-permite-parcelamento-operadora"
				id="check-permite-parcelamento-operadora"
				label="Permite parcelamento pela operadora"></pacto-cat-checkbox>
			<pacto-cat-checkbox
				*ngIf="form.get('parcelamentoOperadora').value"
				[control]="form.get('parcelamentoOperadoraDuracao')"
				class="mt-16 mb-16 display-block"
				i18n-label="@@adm:label-permite-parcelamento-operadora-duracao"
				id="check-permite-parcelamento-operadora-duracao"
				label="Número de vezes de acordo com a duração do contrato/plano"></pacto-cat-checkbox>
			<pacto-cat-form-select
				[control]="form.get('maximoVezesParcelar')"
				[items]="listaParcelas"
				i18n-label="@@adm:label-maximo-vezes-parcelar"
				id="select-maximo-vezes-parcelar"
				idKey="value"
				label="Número máximo de parcelas"></pacto-cat-form-select>
		</ng-container>
	</ng-template>
	<ng-template
		i18n-label="@@adm:tab-label-cobrar-adesao-separada"
		label="Cobrar matrícula separada"
		pactoTabVertical="cobrar-matricula-separada">
		<pacto-cat-checkbox
			[control]="form.get('cobrarAdesaoSeparada')"
			class="mt-16 mb-16 display-block"
			i18n-label="@@adm:label-permite-cobrar-adesao-separada"
			id="check-permite-cobrar-adesao-separada"
			label="Permite cobrar matrícula separada"></pacto-cat-checkbox>
		<pacto-cat-form-select
			[control]="form.get('nrVezesParcelarAdesao')"
			[items]="listaParcelas"
			i18n-label="@@adm:label-numero-maximo-parcelas"
			id="select-numero-max-parcelas"
			idKey="value"
			label="Número máximo de parcelas"></pacto-cat-form-select>
	</ng-template>
	<ng-template
		i18n-label="@@adm:tab-label-cobrar-produtos-separado"
		label="Cobrar produtos separado"
		pactoTabVertical="cobrar-produtos-separado">
		<pacto-cat-checkbox
			[control]="form.get('cobrarProdutoSeparado')"
			i18n-label="@@adm:label-permite-cobrar-produtos-separados"
			id="check-permite-cobrar-produtos-separados"
			label="Permite cobrar produtos separados"></pacto-cat-checkbox>
		<pacto-cat-form-select
			[control]="form.get('nrVezesParcelarProduto')"
			[items]="listaParcelas"
			i18n-label="@@adm:label-parcelar-produtos"
			id="select-parcelar-produtos"
			idKey="value"
			label="Parcelar produtos"></pacto-cat-form-select>
	</ng-template>
	<ng-template
		i18n-label="@@adm:tab-label-desconto"
		label="Desconto"
		pactoTabVertical="desconto">
		<pacto-cat-form-select-filter
			[addEmptyOption]="true"
			[control]="form.get('descontoAntecipado')"
			[endpointUrl]="admRestService.buildFullUrlPlano('desconto')"
			[paramBuilder]="descontoSelectBuilder"
			i18n-label="@@adm:label-desconto-antecipado"
			id="select-desconto-antecipado"
			idKey="codigo"
			label="Desconto em renovação antecipada"
			labelKey="descricao"></pacto-cat-form-select-filter>
		<pacto-cat-checkbox
			[control]="form.get('aceitaDescontoExtra')"
			i18n-label="@@adm:label-aceita-desconto-extra"
			id="check-aceita-desconto-extra"
			label="Aceita dar desconto extra"></pacto-cat-checkbox>
	</ng-template>
</pacto-cat-tabs-vertical>

<div class="action-container btn-row-adm">
	<pacto-cat-button
		(click)="salvar()"
		[type]="buttonType.PRIMARY"
		i18n-label="@@adm:btn-salvar-config"
		id="btn-save-config"
		label="Salvar Configurações"></pacto-cat-button>
</div>

<ng-template #columnNumeroMeses>
	<span i18n="@@adm:pc-column-duracao-contrato">Duração do contrato</span>
</ng-template>

<ng-template #columnCarencia>
	<span i18n="@@adm:column-carencia">Dias de férias</span>
</ng-template>

<pacto-traducoes-xingling #traducao>
	<span i18n="@@adm:label-parcela" xingling="label-parcela">Parcela</span>

	<span i18n="@@adm:mes-janeiro" xingling="mes-janeiro">Janeiro</span>
	<span i18n="@@adm:mes-janeiro" xingling="mes-fevereiro">Fevereiro</span>
	<span i18n="@@adm:mes-janeiro" xingling="mes-marco">Março</span>
	<span i18n="@@adm:mes-janeiro" xingling="mes-abril">Abril</span>
	<span i18n="@@adm:mes-janeiro" xingling="mes-maio">Maio</span>
	<span i18n="@@adm:mes-janeiro" xingling="mes-junho">Junho</span>
	<span i18n="@@adm:mes-janeiro" xingling="mes-julho">Julho</span>
	<span i18n="@@adm:mes-janeiro" xingling="mes-agosto">Agosto</span>
	<span i18n="@@adm:mes-janeiro" xingling="mes-setembro">Setembro</span>
	<span i18n="@@adm:mes-janeiro" xingling="mes-outubro">Outubro</span>
	<span i18n="@@adm:mes-janeiro" xingling="mes-novembro">Novembro</span>
	<span i18n="@@adm:mes-janeiro" xingling="mes-dezembro">Dezembro</span>
</pacto-traducoes-xingling>
