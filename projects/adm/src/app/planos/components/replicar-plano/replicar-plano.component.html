<pacto-cat-card-plain>
	<div class="container-summary">
		<div class="box-title">
			<span class="summary-title">Replicar empresa</span>
		</div>
		<div class="container-amount">
			<div class="box-amount">
				<span class="number">{{ this.qtdeEmpresasRede }}</span>
				<span class="number-title">Unidades</span>
			</div>
			<div class="box-amount">
				<span class="number">{{ this.qtdePlanosReplicados }}</span>
				<span class="number-title">Replicadas</span>
			</div>
			<div class="box-amount">
				<span class="number">{{ this.qtdePlanosNaoReplicados }}</span>
				<span class="number-title">Não replicadas</span>
			</div>
		</div>
	</div>
	<pacto-relatorio
		#grid
		(rowCheck)="rowCheckHandler($event)"
		*ngIf="dataGridConfig"
		[alternatingColors]="'first'"
		[customActions]="botaoReplicar"
		[filterConfig]="gridFilterConfig"
		[showShare]="true"
		[table]="dataGridConfig"
		telaId="replicarPlano"></pacto-relatorio>
</pacto-cat-card-plain>

<ng-template #celulaSituacao let-planoRedeEmpresa="item">
	<div class="status {{ statusClass(planoRedeEmpresa) }}">
		{{ status(planoRedeEmpresa) }}
	</div>
</ng-template>

<ng-template #celulaVinculo let-planoRedeEmpresa="item">
	<div class='vinculo' *ngIf='vinculada(planoRedeEmpresa)'>
		<pacto-cat-button
			(click)="removerVinculoReplicacao(planoRedeEmpresa.chave, planoRedeEmpresa.codigoEmpresaDestino)"
			[label]="'Retirar Vínculo'"
			[loading]="loadingButton(planoRedeEmpresa.chave)"
			[size]="'NORMAL'"
			[type]="'OUTLINE'"></pacto-cat-button>
	</div>
</ng-template>

<ng-template #celulaReplicar let-planoRedeEmpresa="item">
		<pacto-cat-button
			(click)='replicarPlano(planoRedeEmpresa.chave, planoRedeEmpresa.codigoEmpresaDestino)'
			[label]="'Replicar'"
			[loading]='loadingButton(planoRedeEmpresa.chave)'
			[size]="'NORMAL'"
			[type]="'OUTLINE'"></pacto-cat-button>
</ng-template>

<ng-template #botaoReplicar>
	<pacto-cat-button
		(click)="replicarPlanosSelecionados()"
		[full]="true"
		[icon]="'send'"
		[label]="'Replicar selecionadas'"
		[size]="'LARGE'"
		[type]="'ACTION'"></pacto-cat-button>
</ng-template>
