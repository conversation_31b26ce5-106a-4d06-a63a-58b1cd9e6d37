import {
	ChangeDetectorRef,
	Component,
	Input,
	OnInit,
	ViewChild,
} from "@angular/core";
import { OamdService, SessionService } from "sdk";
import {
	GridFilterConfig,
	GridFilterType,
	PactoDataGridConfig,
	RelatorioComponent,
} from "ui-kit";
import { Plano } from "../../plano.model";
import { PlanoRedeEmpresa } from "../../services/plano-rede-empresa.model";
import { PlanoService } from "../cadastrar-plano/plano.service";

@Component({
	selector: "adm-replicar-plano",
	templateUrl: "./replicar-plano.component.html",
	styleUrls: ["./replicar-plano.component.scss"],
})
export class ReplicarPlanoComponent implements OnInit {
	dataGridConfig: PactoDataGridConfig;
	gridFilterConfig: GridFilterConfig;
	@ViewChild("celulaSituacao", { static: true }) celulaSituacao;
	@ViewChild("celulaVinculo", { static: true }) celulaVinculo;
	@ViewChild("celulaReplicar", { static: true }) celulaReplicar;
	@ViewChild("grid", { static: false }) grid: RelatorioComponent;
	@Input() plano: Plano;
	loading: Array<string> = [];
	qtdePlanosReplicados = 0;
	qtdePlanosNaoReplicados = 0;
	qtdeEmpresasRede = 0;
	empresasSelecionadas: Array<PlanoRedeEmpresa> = [];
	planoEmpresaReplicar: PlanoRedeEmpresa[] = [];
	marcouSelecionarTodos = false;

	constructor(
		private oamdService: OamdService,
		private planoService: PlanoService,
		private sessionService: SessionService,
		private cd: ChangeDetectorRef
	) {}

	ngOnInit() {
		this.obterListaEmpresasReplicar();
		this.initPlanosReplicados();
	}

	initPlanosReplicados() {
		this.initRelatorioConfig();
		this.cd.detectChanges();
	}

	async obterListaEmpresasReplicar(): Promise<PlanoRedeEmpresa[]> {
		this.planoEmpresaReplicar = [];
		await this.oamdService
			.findEmpresasRedeReplicar(
				this.plano.codigo,
				this.sessionService.chave,
				this.sessionService.token
			)
			.subscribe((dados: PlanoRedeEmpresa[]) => {
				this.planoEmpresaReplicar = dados;
				this.qtdeEmpresasRede = dados[0].qtdeEmpresasRede;
				this.qtdePlanosReplicados = dados[0].qtdePlanosReplicados;
				this.qtdePlanosNaoReplicados = dados[0].qtdePlanosNaoReplicados;
			});
		return this.planoEmpresaReplicar;
	}

	rowCheckHandler(itemCheck: {
		row?: PlanoRedeEmpresa;
		checked?: boolean;
		selectedAll: boolean;
		clearAll: boolean;
	}) {
		if (itemCheck.selectedAll) {
			this.marcouSelecionarTodos = true;
			this.grid.selectedItems = [];
			this.planoEmpresaReplicar.map((f) => {
				this.grid.selectedItems.push(f);
			});
		} else if (itemCheck.clearAll) {
			this.marcouSelecionarTodos = false;
			this.grid.selectedItems = [];
			this.empresasSelecionadas = [];
			this.grid.reloadData();
		} else if (this.marcouSelecionarTodos) {
			let entrou = false;
			this.grid.selectedItems.forEach((item) => {
				if (
					this.grid.selectedItems.length === 2 &&
					item.chave === itemCheck.row.chave &&
					item.codigoEmpresaDestino === itemCheck.row.codigoEmpresaDestino
				) {
					entrou = true;
					this.grid.selectedItems = [];
				}
			});
			this.grid.selectedItems.forEach((item, index) => {
				if (
					item.chave === itemCheck.row.chave &&
					item.codigoEmpresaDestino === itemCheck.row.codigoEmpresaDestino
				) {
					entrou = true;
					this.grid.selectedItems.splice(index, 1);
				}
			});
			if (!entrou) {
				this.grid.selectedItems.push(itemCheck.row);
			}
		}
		this.empresasSelecionadas =
			this.grid.selectedItems.length > 0 ? this.grid.selectedItems : [];
		if (itemCheck.selectedAll) {
			this.grid.reloadData();
		}
	}

	initRelatorioConfig() {
		this.gridFilterConfig = {
			filters: [
				{
					name: "situacao",
					label: "Situação",
					type: GridFilterType.MANY,
					options: [
						{ value: "REPLICADO", label: "Replicado" },
						{ value: "NAO_REPLICADO", label: "Não replicado" },
					],
				},
			],
		};
		this.dataGridConfig = new PactoDataGridConfig({
			endpointUrl: this.oamdService.urlEmpresasRedeReplicarPlano(
				this.plano.codigo,
				this.sessionService.chave,
				this.sessionService.token
			),
			quickSearch: true,
			exportButton: true,
			showFilters: true,
			pagination: false,
			totalRow: false,
			valueRowCheck: "nomeUnidade",
			columns: [
				{
					nome: "nomeUnidade",
					titulo: "Nome da unidade",
					visible: true,
					ordenavel: false,
				},
				{ nome: "chave", titulo: "Chave", visible: true, ordenavel: false },
				{
					nome: "mensagemSituacao",
					titulo: "Mensagem",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "replicar",
					titulo: "",
					visible: true,
					ordenavel: false,
					celula: this.celulaReplicar,
				},
				{
					nome: "situacao",
					titulo: "Situação",
					visible: true,
					ordenavel: false,
					celula: this.celulaSituacao,
				},
				{
					nome: "vinculo",
					titulo: "Vínculo",
					visible: true,
					ordenavel: false,
					celula: this.celulaVinculo,
				}
			],
		});
	}

	statusClass(planoRedeEmpresa: PlanoRedeEmpresa) {
		return this.status(planoRedeEmpresa)
			.normalize("NFD")
			.replace(/[\u0300-\u036f]/g, "")
			.replace(" ", "_");
	}

	vinculada(planoRedeEmpresa: PlanoRedeEmpresa): boolean {
		return (planoRedeEmpresa && (planoRedeEmpresa.dataAtualizacao !== null));
	}

	status(planoRedeEmpresa: PlanoRedeEmpresa) {
		if (this.loadingButton(planoRedeEmpresa.chave)) {
			return "REPLICANDO";
		}

		if (
			planoRedeEmpresa.mensagemSituacao === undefined ||
			planoRedeEmpresa.mensagemSituacao === null
		) {
			return "NAO REPLICADA";
		}

		if (
			planoRedeEmpresa.mensagemSituacao &&
			planoRedeEmpresa.mensagemSituacao
				.toUpperCase()
				.indexOf("REPLICADO EM") !== -1
		) {
			return "REPLICADA";
		}

		return "NAO_REPLICADA";
	}

	textoBotaoReplicar(planoRedeEmpresa: PlanoRedeEmpresa) {
		if (this.loadingButton(planoRedeEmpresa.chave)) {
			return "Replicando plano...";
		}
		if (
			this.exibirBotaoReplicar(planoRedeEmpresa) &&
			this.exibirMensagemSituacao(planoRedeEmpresa)
		) {
			return "Tentar novamente";
		} else {
			return "Replicar";
		}
	}

	exibirBotaoReplicar(planoRedeEmpresa: PlanoRedeEmpresa) {
		return (
			planoRedeEmpresa.mensagemSituacao === null ||
			planoRedeEmpresa.mensagemSituacao === undefined ||
			(planoRedeEmpresa &&
				planoRedeEmpresa.mensagemSituacao.indexOf("REPLICADO") === -1)
		);
	}

	exibirMensagemSituacao(planoRedeEmpresa: PlanoRedeEmpresa) {
		return (
			planoRedeEmpresa &&
			planoRedeEmpresa.mensagemSituacao &&
			planoRedeEmpresa.mensagemSituacao.length > 0 &&
			planoRedeEmpresa.mensagemSituacao.indexOf("Não replicado") === -1
		);
	}

	loadingButton(chave: string) {
		return this.loading.indexOf(chave) > -1;
	}

	replicarPlanosSelecionados() {
		this.empresasSelecionadas.forEach(async (emp: PlanoRedeEmpresa) => {
			await this.replicarPlano(emp.chave, emp.codigoEmpresaDestino);
		});
	}

	async replicarPlano(chave: string, empresa: number) {
		const index = this.loading.push(chave);
		try {
			const novoPlanoReplicado: PlanoRedeEmpresa = await this.planoService
				.replicar(this.plano.codigo, chave, empresa)
				.toPromise();
			(await this.obterListaEmpresasReplicar()).map((planoReplicado) => {
				if (planoReplicado.chave === novoPlanoReplicado.chave) {
					return novoPlanoReplicado;
				} else {
					return planoReplicado;
				}
			});
			this.qtdePlanosReplicados++;
			this.qtdePlanosNaoReplicados--;
		} catch (error) {
			if (error && error.error && error.error.meta) {
				const { message } = error.error.meta;
				(await this.obterListaEmpresasReplicar()).map(
					(planoReplicado: PlanoRedeEmpresa) => {
						if (planoReplicado.chave === chave) {
							planoReplicado.mensagemSituacao = message;
						}
						return planoReplicado;
					}
				);
			}
		}
		this.loading = this.loading.splice(index, 1);
		this.grid.reloadData();
		this.cd.detectChanges();
	}

	async removerVinculoReplicacao(chave: string, empresa: number) {
		const index = this.loading.push(chave);
		try {
			const novoPlanoReplicado: PlanoRedeEmpresa = await this.planoService
				.removerVinculoReplicacao(this.plano.codigo, chave, empresa)
				.toPromise();
			(await this.obterListaEmpresasReplicar()).map((planoReplicado) => {
				if (planoReplicado.chave === novoPlanoReplicado.chave) {
					return novoPlanoReplicado;
				} else {
					return planoReplicado;
				}
			});
			this.qtdePlanosReplicados--;
			this.qtdePlanosNaoReplicados++;
		} catch (error) {
			const { message } = error.error.meta;
			(await this.obterListaEmpresasReplicar()).map(
				(planoReplicado: PlanoRedeEmpresa) => {
					if (planoReplicado.chave === chave) {
						planoReplicado.mensagemSituacao = message;
					}
					return planoReplicado;
				}
			);
		}
		this.loading = this.loading.splice(index, 1);
		this.grid.reloadData();
		this.cd.detectChanges();
	}

}
