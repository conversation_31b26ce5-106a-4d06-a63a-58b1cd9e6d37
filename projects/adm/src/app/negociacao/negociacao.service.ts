import { Injectable } from "@angular/core";
import {
	AdmCoreApiNegociacaoService,
	ContratoNegociacao,
	Negociacao,
} from "adm-core-api";
import { BehaviorSubject, Observable, of, Subscription } from "rxjs";
import { SnotifyService } from "ng-snotify";
import { NegociacaoZwBootService } from "adm-legado-api";
import { PlataformaModulo, SessionService } from "sdk";

@Injectable({
	providedIn: "root",
})
export class NegociacaoService {
	private negociacaoSubject = new BehaviorSubject<Negociacao>(new Negociacao());
	private simuladoSubject = new BehaviorSubject<any>({});
	private clienteSubject = new BehaviorSubject<any>({});
	private configsContrato: any = {};
	public usuarioDataBase;
	public usuarioPeriodo;
	public usuarioTipo;
	private simulacaoSubscription: Subscription;

	constructor(
		private service: AdmCoreApiNegociacaoService,
		private sessionService: SessionService,
		private serviceZWBoot: NegociacaoZwBootService,
		private notificationService: SnotifyService
	) {}

	simular(): Observable<void> {
		return new Observable((observer) => {
			if (this.simulacaoSubscription) {
				this.simulacaoSubscription.unsubscribe();
			}

			if (
				this.configsContrato &&
				this.configsContrato.plano &&
				this.configsContrato.duracao
			) {
				this.converterQtd();
				this.simulacaoSubscription = this.simularCall().subscribe(
					(contrato) => {
						if (contrato.error) {
							if (
								!contrato.error.includes(
									"manual excedem o permitido para o perfil do"
								) &&
								!contrato.error.includes("excedem o permitido para a duração")
							) {
								this.notificationService.error(contrato.error);
							}
							observer.error(contrato.error);
							return;
						}
						this.updateSimulado(contrato);
						observer.next();
						observer.complete();
					},
					(httpErrorResponse) => {
						const err = httpErrorResponse.error;
						err.replace("ERROR: trigger:", "");
						if (err.meta && err.meta.messageValue) {
							this.notificationService.error(err.meta.messageValue);
							observer.error(err.meta.messageValue);
						} else {
							const errorMessage =
								"Ocorreu um erro inesperado, tente novamente.";
							this.notificationService.error(errorMessage);
							observer.error(errorMessage);
						}
					}
				);
			} else {
				this.updateSimulado({});
				observer.next();
				observer.complete();
			}
		});
	}

	simularCall() {
		return this.sessionService.modulosHabilitados.includes(PlataformaModulo.ZWB)
			? this.serviceZWBoot.simular(this.configsContrato)
			: this.service.simular(this.configsContrato);
	}

	gravar(): Observable<any> {
		this.converterQtd();
		return this.sessionService.modulosHabilitados.includes(PlataformaModulo.ZWB)
			? this.serviceZWBoot.gravar(this.configsContrato)
			: this.service.gravar(this.configsContrato);
	}

	public converterQtd() {
		if (this.configsContrato && this.configsContrato.produtos) {
			this.configsContrato.produtos.forEach((produto) => {
				if (produto.qtdFC) {
					produto.qtdFC = null;
				}
				if (
					typeof produto.quantidade === "object" &&
					produto.quantidade !== null
				) {
					if (
						produto.quantidade.hasOwnProperty("id") &&
						typeof produto.quantidade.id === "number"
					) {
						produto.quantidade = produto.quantidade.id;
					}
				}
			});
		}
	}

	getConfigsContrato() {
		return this.configsContrato;
	}

	setConfigsContrato(configsContrato) {
		this.configsContrato = configsContrato;
	}

	get() {
		return this.negociacaoSubject.asObservable();
	}

	getSimulado() {
		return this.simuladoSubject.asObservable();
	}

	getCliente() {
		return this.clienteSubject.asObservable();
	}

	update(negociacao: Negociacao) {
		this.negociacaoSubject.next(negociacao);
	}

	updateSimulado(simulado) {
		this.simuladoSubject.next(simulado);
	}

	updateCliente(cliente) {
		this.clienteSubject.next(cliente);
	}
}
