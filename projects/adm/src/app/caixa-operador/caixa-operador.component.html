<adm-layout
	pageTitle="Fechamento de caixa por operador"
	(goBack)="voltarHome()"
	[showBtnPlanoAntigo]="false">
	<pacto-cat-card-plain class="form-card">
		<form>
			<div class="filtros-direita">
				<div class="filtros-direita-item">
					<ds3-form-field *ngIf="temPermissaoEmpresas947">
						<ds3-field-label>Empresa</ds3-field-label>
						<ds3-select
							id="select-empresa"
							[options]="empresas"
							[valueKey]="'codigo'"
							[nameKey]="'nome'"
							[formControl]="form.get('empresa')"
							ds3Input></ds3-select>
					</ds3-form-field>

					<ds3-form-field>
						<ds3-field-label>Faturamento Recebido</ds3-field-label>
						<ds3-input-date
							[position]="'middle-right'"
							ds3Input
							dateType="dateranger"
							[controlStart]="form.get('faturamentoDe')"
							[controlEnd]="form.get('faturamentoAte')"></ds3-input-date>
					</ds3-form-field>

					<ds3-form-field>
						<ds3-field-label>Faturamento Produto</ds3-field-label>
						<ds3-input-date
							ds3Input
							[position]="'middle-right'"
							dateType="dateranger"
							[controlStart]="form.get('faturamentoProdutoDe')"
							[controlEnd]="form.get('faturamentoProdutoAte')"></ds3-input-date>
					</ds3-form-field>

					<ds3-form-field>
						<ds3-field-label>Faixa de horário</ds3-field-label>
						<div class="camposPeriodo">
							<ds3-input-date
								ds3Input
								dateType="timepicker"
								[control]="form.get('horarioDe')"></ds3-input-date>
							<span>até</span>
							<ds3-input-date
								ds3Input
								dateType="timepicker"
								[control]="form.get('horarioAte')"></ds3-input-date>
						</div>
					</ds3-form-field>

					<ds3-form-field *ngIf="temPermissao618">
						<ds3-field-label>Operadores</ds3-field-label>
						<ds3-select-multi
							id="select-operadores"
							[valueKey]="'codigo'"
							[nameKey]="'nome'"
							[options]="operadores"
							[formControl]="form.get('operadores')"
							ds3Input></ds3-select-multi>
					</ds3-form-field>

					<ds3-form-field *ngIf="!temPermissao618">
						<ds3-field-label>Nome do operador</ds3-field-label>
						<input ds3Input [formControl]="operadorFC" />
					</ds3-form-field>
				</div>
				<div class="filtros-direita-item">
					<ds3-form-field>
						<ds3-field-label>Tipo de comprador</ds3-field-label>
						<ds3-select
							id="select-tipo-comprador"
							[options]="tiposComprador"
							[valueKey]="'value'"
							[nameKey]="'label'"
							[formControl]="form.get('tipoComprador')"
							ds3Input></ds3-select>
					</ds3-form-field>

					<ds3-form-field *ngIf="temCE">
						<ds3-field-label>Fonte de dados</ds3-field-label>
						<ds3-select
							id="select-fonte-dados"
							[options]="fontesDados"
							[valueKey]="'value'"
							[nameKey]="'label'"
							[formControl]="form.get('fonte')"
							ds3Input></ds3-select>
					</ds3-form-field>

					<ds3-form-field *ngIf="convenios">
						<ds3-field-label>Convênios de cobrança</ds3-field-label>
						<ds3-select-multi
							id="select-convenio"
							[valueKey]="'codigo'"
							[nameKey]="'descricao'"
							[options]="convenios"
							[formControl]="form.get('convenios')"
							ds3Input></ds3-select-multi>
					</ds3-form-field>

					<ds3-form-field>
						<ds3-checkbox ds3Input [formControl]="form.get('usuarioAdmin')">
							Considerar usuário ADMIN
						</ds3-checkbox>
					</ds3-form-field>

					<ds3-form-field>
						<ds3-checkbox
							ds3Input
							[formControl]="form.get('usuarioRecorrencia')">
							Considerar usuário RECORRENCIA
						</ds3-checkbox>
					</ds3-form-field>

					<ds3-form-field>
						<ds3-checkbox ds3Input [formControl]="form.get('layoutLivroCaixa')">
							Layout Livro Caixa
						</ds3-checkbox>
					</ds3-form-field>

					<ds3-form-field>
						<ds3-checkbox
							ds3Input
							[formControl]="form.get('layoutImpressaoTermica')">
							Layout Impressão Térmica
						</ds3-checkbox>
					</ds3-form-field>

					<ds3-form-field>
						<ds3-checkbox
							ds3Input
							[formControl]="form.get('somenteTotalizadores')">
							Somente Totalizadores
						</ds3-checkbox>
					</ds3-form-field>
					
					<ds3-form-field>
						<ds3-checkbox
							ds3Input
							[formControl]="form.get('somenteTotalizadores')">
							Somente Totalizadores
						</ds3-checkbox>
					</ds3-form-field>

					<ds3-form-field>
						<ds3-field-label>Ordenação</ds3-field-label>
						<ds3-select
							id="select-ordenacao"
							[options]="ordenacaoOptions"
							[valueKey]="'value'"
							[nameKey]="'label'"
							[formControl]="form.get('ordenacao')"
							ds3Input></ds3-select>
					</ds3-form-field>
				</div>
			</div>
		</form>

		<div class="footer">
			<button id="btn-imprimir" ds3-flat-button (click)="imprimir()">
				Gerar relatório
			</button>
		</div>
	</pacto-cat-card-plain>
</adm-layout>
