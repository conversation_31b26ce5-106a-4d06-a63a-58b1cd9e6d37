import { ChangeDetector<PERSON><PERSON>, Component, OnInit, ViewChild } from "@angular/core";
import { FormBuilder, FormControl, FormGroup } from "@angular/forms";
import { SnotifyService } from "ng-snotify";
import { SessionService } from "@base-core/client/session.service";
import { AdmRestService } from "@adm/adm-rest.service";
import { ZwBootRelatorioFechamentoCaixaOperadorService } from "adm-legado-api";
import { PlataformaModulo } from "sdk";
import { SelectFilterParamBuilder } from "ui-kit";
import { PermissaoService } from "pacto-layout";
import { Ds3SelectComponent } from "../../../../ui/src/lib/ds3/ds3-forms/ds3-select/ds3-select.component";
import moment from "moment";
@Component({
	selector: "adm-caixa-operador",
	templateUrl: "./caixa-operador.component.html",
	styleUrls: ["./caixa-operador.component.scss"],
})
export class CaixaOperadorComponent implements OnInit {
	public form: FormGroup;
	@ViewChild("operadorSelect", { static: true })
	operadorSelect: Ds3SelectComponent;
	empresas = [];
	tiposComprador = [];
	fontesDados = [];
	convenios = [];
	operadores = [];
	operadorFC;
	temPermissao618 = false;
	temPermissaoEmpresas947 = false;
	ordenacaoOptions = [
		{ value: "NR", label: "Número do Recibo" },
		{ value: "NA", label: "Nome do Aluno" },
		{ value: "NC", label: "Nome do Colaborador" },
	];
	selectBuilder: SelectFilterParamBuilder = (term) => {
		return {
			quickSearch: term,
		};
	};

	constructor(
		private readonly fb: FormBuilder,
		private readonly cd: ChangeDetectorRef,
		private notificationService: SnotifyService,
		private readonly relatorioFechamentoCaixaOperadorService: ZwBootRelatorioFechamentoCaixaOperadorService,
		private admRestService: AdmRestService,
		private session: SessionService,
		private readonly sessionService: SessionService,
		private permissaoService: PermissaoService
	) {}

	ngOnInit() {
		this.init();
	}

	init() {
		this.form = this.fb.group({
			empresa: new FormControl(Number(this.sessionService.empresaId)),
			layoutLivroCaixa: new FormControl(false),
			somenteTotalizadores: new FormControl(false),
			layoutImpressaoTermica: new FormControl(false),
			layoutImpressaoTermicaResumido: new FormControl(false),
			faturamentoDe: new FormControl(new Date()),
			faturamentoAte: new FormControl(new Date()),
			usuarioRecorrencia: new FormControl(false),
			usuarioAdmin: new FormControl(false),
			faturamentoProdutoDe: new FormControl(),
			faturamentoProdutoAte: new FormControl(),
			operador: new FormControl(),
			convenios: new FormControl(),
			operadores: new FormControl(),
			horarioDe: new FormControl(),
			fonte: new FormControl("3"),
			tipoComprador: new FormControl("T"),
			horarioAte: new FormControl(),
			ordenacao: new FormControl("NR"),
		});
		this.temPermissaoEmpresas947 =
			this.permissaoService.temPermissaoAdm("9.47");
		this.temPermissao618 = this.permissaoService.temPermissaoAdm("6.18");
		if (this.temPermissao618 === false) {
			const operadore = [];
			operadore.push(Number(this.session.perfilUsuarioAdm.user.id));
			this.form.get("operadores").setValue(operadore);
			this.operadorFC = new FormControl(
				this.session.perfilUsuarioAdm.user.nome
			);
			this.operadorFC.disable({ emitEvent: false });
			this.form.get("operadores").disable({ emitEvent: false });
		}

		this.form
			.get("layoutLivroCaixa")
			.valueChanges.subscribe((value: boolean) => {
				const somenteTotalizadores = this.form.get("somenteTotalizadores");
				if (value) {
					somenteTotalizadores.setValue(false);
					somenteTotalizadores.disable({ emitEvent: false });
				} else {
					somenteTotalizadores.enable({ emitEvent: false });
				}
			});
		this.form.get("empresa").valueChanges.subscribe(() => {
			this.convenios = null;
			this.form.get("convenios").setValue(null);
			this.cd.detectChanges();
			this.relatorioFechamentoCaixaOperadorService
				.convenios(this.form.get("empresa").value)
				.subscribe((convenios) => {
					this.convenios = convenios.content;
					this.cd.detectChanges();
				});
			this.relatorioFechamentoCaixaOperadorService
				.operadores("", this.form.get("empresa").value)
				.subscribe((usu) => {
					this.operadores = usu.content;
					this.cd.detectChanges();
				});
		});

		this.empresas = [
			{ codigo: 9999, nome: "TODAS AS EMPRESAS" },
			...this.sessionService.empresas,
		];
		this.relatorioFechamentoCaixaOperadorService
			.convenios(this.sessionService.empresaId)
			.subscribe((convenios) => {
				this.convenios = convenios.content;
				this.tiposComprador = [
					{ value: "T", label: "Todos" },
					{ value: "CLI", label: "Cliente" },
					{ value: "COL", label: "Colaborador" },
					{ value: "CN", label: "Consumidor" },
				];
				this.fontesDados = [
					{ value: "1", label: "ADM" },
					{ value: "4", label: "Central de eventos" },
					{ value: "5", label: "Todas, exceto financeiro" },
					{ value: "3", label: "Todas" },
				];
				this.cd.detectChanges();
			});
		this.relatorioFechamentoCaixaOperadorService
			.operadores("", this.sessionService.empresaId)
			.subscribe((usu) => {
				this.operadores = usu.content;
				this.cd.detectChanges();
			});
	}

	imprimir() {
		const faturamentoDe = this.form.get("faturamentoDe").value;
		const faturamentoAte = this.form.get("faturamentoAte").value;
		const faturamentoProdutoDe = this.form.get("faturamentoProdutoDe").value;
		const faturamentoProdutoAte = this.form.get("faturamentoProdutoAte").value;
		if (!faturamentoDe || !faturamentoAte) {
			this.notificationService.warning(
				"Não é possível emitir o relatório. Informe primeiro o Período da pesquisa!"
			);
			return;
		}
		if (faturamentoDe && faturamentoAte && faturamentoDe > faturamentoAte) {
			this.notificationService.warning(
				"A data de início deve ser menor que a data de término para a pesquisa!"
			);
			return;
		}

		if (
			faturamentoProdutoDe &&
			faturamentoProdutoAte &&
			faturamentoProdutoDe > faturamentoProdutoAte
		) {
			this.notificationService.warning(
				"A data de início do faturamento do produto deve ser menor que a data de término para a pesquisa!"
			);
			return;
		}

		this.relatorioFechamentoCaixaOperadorService
			.imprimir(this.filtros())
			.subscribe(
				(response) => {
					if (response.content === "sem_registros") {
						this.notificationService.warning(
							"Não foi encontrado nenhum recibo no período informado."
						);
					} else {
						window.open(response.content, "_blank");
					}
				},
				(e) => {
					if (e.error.meta && e.error.meta.message) {
						this.notificationService.error(e.error.meta.message);
					} else {
						this.notificationService.error("Erro ao gerar relatorio!");
					}
				}
			);
	}

	filtros() {
		const filters = {
			empresa: this.form.get("empresa").value,
			layoutLivroCaixa: this.form.get("layoutLivroCaixa").value,
			somenteTotalizadores: this.form.get("somenteTotalizadores").value,
			layoutImpressaoTermica: this.form.get("layoutImpressaoTermica").value,
			layoutImpressaoTermicaResumido: this.form.get("layoutImpressaoTermicaResumido").value,
			faturamentoDe: this.form.get("faturamentoDe").value
				? moment(this.form.get("faturamentoDe").value)
						.startOf("day")
						.format("yyyy-MM-DD")
				: null,
			faturamentoAte: this.form.get("faturamentoAte").value
				? moment(this.form.get("faturamentoAte").value)
						.endOf("day")
						.format("yyyy-MM-DD")
				: null,
			usuarioRecorrencia: this.form.get("usuarioRecorrencia").value,
			usuarioAdmin: this.form.get("usuarioAdmin").value,
			faturamentoProdutoDe: this.form.get("faturamentoProdutoDe").value
				? moment(this.form.get("faturamentoProdutoDe").value)
						.startOf("day")
						.format("yyyy-MM-DD")
				: null,
			faturamentoProdutoAte: this.form.get("faturamentoProdutoAte").value
				? moment(this.form.get("faturamentoProdutoAte").value)
						.endOf("day")
						.format("yyyy-MM-DD")
				: null,
			operadores: this.form.get("operadores").value,
			horarioDe: this.form.get("horarioDe").value
				? this.form.get("horarioDe").value
				: moment().startOf("day").format("HH:mm"),
			fonte: this.form.get("fonte").value,
			convenios: this.form.get("convenios").value,
			tipoComprador: this.form.get("tipoComprador").value,
			horarioAte: this.form.get("horarioAte").value
				? this.form.get("horarioAte").value
				: moment().endOf("day").format("HH:mm"),
			ordenacao: this.form.get("ordenacao").value,
		};
		return filters;
	}

	voltarHome(): void {
		window.history.back();
	}

	get _rest() {
		return this.admRestService;
	}

	get temCE(): boolean {
		return (
			this.session.modulosHabilitados &&
			this.session.modulosHabilitados.some(
				(modulo) => PlataformaModulo.CE === modulo
			)
		);
	}
}
