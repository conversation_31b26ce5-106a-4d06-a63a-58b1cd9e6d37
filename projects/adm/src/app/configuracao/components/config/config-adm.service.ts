import { EventEmitter, Injectable } from "@angular/core";
import {
	AbstractControl,
	FormControl,
	FormGroup,
	ValidationErrors,
	Validators,
} from "@angular/forms";
import {
	AdmMsApiConfiguracaoClienteService,
	AdmMsApiConfiguracaoColaboradorService,
	AdmMsApiConfiguracaoService,
	AdmMsApiQuestionarioService,
	AdmMsApiRecorrenciaEmailService,
	ConfiguracaoCliente,
	ConfiguracaoColaborador,
	ConfiguracaoSistema,
	EmailRecorrencia,
	Questionario,
} from "adm-ms-api";
import { SnotifyService } from "ng-snotify";
import { Observable, forkJoin, from } from "rxjs";
import { map } from "rxjs/operators";
import { ClientDiscoveryService, SessionService } from "sdk";
import { PactoDataGridColumnConfigDto, PactoDataGridConfig } from "ui-kit";
import {
	PlanoContratoVO,
	ProcessarPagamentoPixVO,
	ProdutosParcelas,
	ZwServletApiProcessosService,
} from "zw-servlet-api";
import {
	ConfigItemSelectOption,
	ConfigItemType,
} from "../inputs/form-configuracoes.model";
import { ConfigModuloSubGroup } from "./model/config-module-subgroup.model";
import { SubGrupoInputs } from "./model/sub-group-inputs.model";
import { ModalService } from "@base-core/modal/modal.service";
import { PermissaoService } from "pacto-layout";

@Injectable({
	providedIn: "root",
})
export class ConfigAdmService {
	currentValuesCamposVisitante: ConfiguracaoCliente[];
	currentValuesCamposCliente: ConfiguracaoCliente[];
	currentValuesCamposColaborador: ConfiguracaoColaborador[];
	formGroupCamposVisitante: FormGroup = new FormGroup({});
	formGroupCamposCliente: FormGroup = new FormGroup({});
	formGroupCamposColaborador: FormGroup = new FormGroup({});
	fromControlReprocessarExtratosDataIncio: FormControl = new FormControl(
		Date.now()
	);
	fromControlReprocessarExtratosDataFim: FormControl = new FormControl(
		Date.now()
	);
	questionarios: Questionario[];
	loaded: EventEmitter<boolean> = new EventEmitter();
	detectChanges: EventEmitter<boolean> = new EventEmitter();
	config: ConfiguracaoSistema;
	loading: boolean = false;

	formControlDataGridPagamentosSemRecibo: FormControl = new FormControl();
	formControlDataGridPagamentoSemProdutosPagos: FormControl = new FormControl();
	formControlDataGridPagamentosSemVinculoParcelas: FormControl =
		new FormControl();
	formControlDataGridParcelasSemProdutos: FormControl = new FormControl();
	formControlDataGridProdutosSemParcelas: FormControl = new FormControl();
	formControlDataGridParcelasEmAbertoComPagamento: FormControl =
		new FormControl();
	formControlDataGridRecibosSemProdutosCC: FormControl = new FormControl();

	formControlProdutoSubstituir: FormControl = new FormControl();
	formControlProdutoSubstituido: FormControl = new FormControl();
	formControlProdutoSubstituidoDeletar: FormControl = new FormControl();

	formControlEmpresaAdicionarModalidade: FormControl = new FormControl();
	formControlModalidadeAdicionarModalidade: FormControl = new FormControl();
	formControlPlanoAdicionarModalidade: FormControl = new FormControl();
	formControlHorarioAdicionarModalidade: FormControl = new FormControl();
	formControlDataGridContratosAdicionarModalidade: FormControl =
		new FormControl();

	formControlEmpresaRemoverModalidade: FormControl = new FormControl();
	formControlModalidadeRemoverModalidade: FormControl = new FormControl();
	formControlPlanoRemoverModalidade: FormControl = new FormControl();
	formControlDataGridContratosRemoverModalidade: FormControl =
		new FormControl();

	formControlPlanoCorrigirFeriasContratos: FormControl = new FormControl();
	formControlTotalContratosCorrigirFerias: FormControl = new FormControl();

	fromControlCorrigirMetaDiariaDuplicados: FormControl = new FormControl(
		new Date()
	);

	formControlCodigosPix: FormControl = new FormControl();
	formControlPixDataPagamento: FormControl = new FormControl(new Date());
	formControlDataGridPixProcessados: FormControl = new FormControl();
	formControlDataGridPixNaoProcessados: FormControl = new FormControl();
	formControlExcluirColaboradores: FormControl = new FormControl();
	formControlExcluirAlunos: FormControl = new FormControl();
	formControlExcluirCliente: FormControl = new FormControl();
	formControlPesquisarMatricula: FormControl = new FormControl();

	formControlUsarConfigSesc: FormControl = new FormControl();
	formControlChavePublicaSesc: FormControl = new FormControl();
	formControlChavePrivadaSesc: FormControl = new FormControl();

	formControlUsarApiSescGO: FormControl = new FormControl();
	formControlUsuarioApiSescGo: FormControl = new FormControl();
	formControlSenhaApiSescGo: FormControl = new FormControl();

	formControlAtivarLoginAzureAD: FormControl = new FormControl();
	formControlAzureADTenantId: FormControl = new FormControl();
	formControlAzureADClientID: FormControl = new FormControl();

	constructor(
		private admClienteService: AdmMsApiConfiguracaoClienteService,
		private admColaboradorService: AdmMsApiConfiguracaoColaboradorService,
		private admMsApiQuestionarioService: AdmMsApiQuestionarioService,
		private discoveryService: ClientDiscoveryService,
		private admMsApiRecorrenciaEmailService: AdmMsApiRecorrenciaEmailService,
		private admConfigApiService: AdmMsApiConfiguracaoService,
		private notificationService: SnotifyService,
		private zwApiServeletProcessos: ZwServletApiProcessosService,
		private sessionService: SessionService,
		private modalService: ModalService,
		private permissaoService: PermissaoService
	) {
		forkJoin(
			this.admConfigApiService.consultar(),
			this.admClienteService.consultar(false),
			this.admClienteService.consultar(true),
			this.admColaboradorService.consultar(null),
			this.admMsApiQuestionarioService.consultar(0, 20, "nomeinterno")
		).subscribe(
			([
				config,
				camposCliente,
				camposVisitante,
				camposColaborador,
				questionarios,
			]) => {
				this.config = config;
				this.currentValuesCamposCliente = camposCliente;
				this.currentValuesCamposVisitante = camposVisitante;
				this.currentValuesCamposColaborador = camposColaborador;
				this.questionarios = questionarios;
			},
			(error) =>
				console.error("Falha ao consultar configurações do adm", error),
			() => {
				this.loaded.emit(true);
			}
		);

		this.prepareDetectChanges();
	}

	prepareDetectChanges() {
		[
			this.formControlDataGridPagamentosSemRecibo,
			this.formControlDataGridPagamentoSemProdutosPagos,
			this.formControlDataGridPagamentosSemVinculoParcelas,
			this.formControlDataGridParcelasSemProdutos,
			this.formControlDataGridProdutosSemParcelas,
			this.formControlDataGridParcelasEmAbertoComPagamento,
			this.formControlDataGridContratosAdicionarModalidade,
			this.formControlDataGridContratosRemoverModalidade,
			this.formControlTotalContratosCorrigirFerias,
			this.formControlDataGridPixProcessados,
			this.formControlDataGridPixNaoProcessados,
		].forEach((formControl) => {
			from(formControl.valueChanges).subscribe(() => {
				this.detectChanges.emit(true);
			});
		});
	}

	getInputs(): SubGrupoInputs[] {
		return [
			this.getInputsBaisco(),
			this.getInputsAcesso(),
			this.getInputsVisitante(),
			this.getInputsCliente(),
			this.getInputsColaborador(),
			this.getInputsOutas(),
			this.getInputsQuestionarios(),
			this.getInputsQuestionarioSessao(),
			this.getInputsRecorrencia(),
			this.getInputProcessosManutencaoProdutosParcelas(),
			this.getInputProcessosManutencaoGerais(),
			this.getInputsConvites(),
		];
	}

	getInputProcessosManutencaoGerais(): SubGrupoInputs {
		return {
			subgrupo: ConfigModuloSubGroup.ADM_MANUTENCAO_GERAIS,
			inputs: [
				{
					title: "Adicionar modalidade em contratos de determinado plano",
					type: ConfigItemType.GROUP,
					description:
						"Objetivo:	Adicionar determinada modalidade em todos os contratos que estão vigentes. A modalidade será inserida com valor zerado e não contabilizará em comissões",
					children: [
						{
							title: "Empresa",
							codigo: false,
							type: ConfigItemType.SELECTFILTER,
							endpointUrl:
								this.discoveryService.getUrlMap().admMsUrl +
								"/v1/empresa/resumo",
							placeholder: "Pesquise uma empresa",
							labelKey: "nome",
							idKey: "codigo",
							formControl: this.formControlEmpresaAdicionarModalidade,
							responseParser: (result: any) => result.content,
							paramBuilder: (nome: string) => {
								return {
									page: "0",
									size: "20",
									nome,
									orderBy: "nome",
								};
							},
						},
						{
							title: "Modalidade",
							codigo: false,
							type: ConfigItemType.SELECTFILTER,
							endpointUrl:
								this.discoveryService.getUrlMap().admMsUrl + "/v1/modalidade",
							placeholder: "Pesquise uma modalidade",
							labelKey: "nome",
							idKey: "codigo",
							formControl: this.formControlModalidadeAdicionarModalidade,
							responseParser: (result: any) => result.content,
							paramBuilder: (nome: string) => {
								return {
									page: "0",
									size: "20",
									nome,
									orderBy: "nome",
								};
							},
						},
						{
							title: "Plano",
							codigo: false,
							type: ConfigItemType.SELECTFILTER,
							endpointUrl:
								this.discoveryService.getUrlMap().admMsUrl + "/v1/plano",
							placeholder: "Pesquise um plano",
							labelKey: "descricao",
							idKey: "codigo",
							formControl: this.formControlPlanoAdicionarModalidade,
							responseParser: (result: any) => result.content,
							paramBuilder: (nome: string) => {
								return {
									page: "0",
									size: "20",
									nome,
									orderBy: "descricao",
								};
							},
						},
						{
							title: "Horário",
							codigo: false,
							type: ConfigItemType.SELECTFILTER,
							endpointUrl:
								this.discoveryService.getUrlMap().admMsUrl + "/v1/horario",
							placeholder: "Pesquise um horário",
							labelKey: "descricao",
							idKey: "codigo",
							formControl: this.formControlHorarioAdicionarModalidade,
							responseParser: (result: any) => result.content,
							paramBuilder: (nome: string) => {
								return {
									page: "0",
									size: "20",
									nome,
									orderBy: "descricao",
								};
							},
						},
						{
							title: "Buscar contratos",
							typeDescription: "Buscar contratos",
							type: ConfigItemType.BUTTON,
							description:
								'A funcionalidade "Buscar contratos" tem o objetivo de localizar os contratos que não possuem a modalidade selecionada,geralmente é utilizada para fins de gestão em academias,  permitindo  ao usuário identificar e gerenciar os contratos que ainda não possuem uma modalidade específica atribuída',
							codigo: false,
							disabled: () => {
								return (
									this.formControlEmpresaAdicionarModalidade.value === null ||
									this.formControlModalidadeAdicionarModalidade.value ===
										null ||
									this.formControlPlanoAdicionarModalidade.value === null
								);
							},
							onClick: () => {
								this.consultarContratosParaAdiconarModalidade();
							},
						},
						{
							title: "Adicionar modalidade aos contratos encontrados",
							typeDescription: "Adicionar modalidade",
							type: ConfigItemType.BUTTON,
							description:
								'A funcionalidade "Adicionar modalidade aos contratos encontrados" permite que o usuário associe uma modalidade a contratos que foram identificados como não tendo uma modalidade configurada. Essa ação é importante para garantir que os contratos estejam corretamente configurados e que os clientes tenham acesso aos serviços que escolheram',
							codigo: false,
							disabled: () =>
								this.formControlDataGridContratosAdicionarModalidade.value ===
									null ||
								this.formControlDataGridContratosAdicionarModalidade.value ===
									undefined ||
								(Array.isArray(
									this.formControlDataGridContratosAdicionarModalidade.value
										.content
								) &&
									this.formControlDataGridContratosAdicionarModalidade.value
										.content.length === 0),
							onClick: () => {
								this.zwApiServeletProcessos
									.adicionarModalidadeEmContratos({
										chave: this.sessionService.chave,
										plano:
											this.formControlPlanoAdicionarModalidade.value.codigo,
										empresa:
											this.formControlEmpresaAdicionarModalidade.value.codigo,
										modalidade:
											this.formControlModalidadeAdicionarModalidade.value
												.codigo,
										usuario: this.sessionService.loggedUser.id,
										descricaoHorario: this.formControlHorarioAdicionarModalidade
											.value
											? this.formControlHorarioAdicionarModalidade.value
													.descricao
											: null,
									})
									.subscribe((msg) => {
										this.notificationService.success(msg);
										this.consultarContratosParaAdiconarModalidade();
									});
							},
						},
						{
							title: "Lista de contratos que serão adicionados a modalidade",
							type: ConfigItemType.GRID,
							description:
								"Tem o objetivo de listar os clientes que possuem contratos que serão vinculados a uma modalidade específica. Isso é útil quando você deseja identificar os contratos que precisam ser associados a uma modalidade específica para que esses clientes possam ter acesso aos serviços ou benefícios relacionados a essa modalidade",
							codigo: false,
							hide: () =>
								this.formControlDataGridContratosAdicionarModalidade.value ===
								null,
							emptyStateMessage:
								"Não existe nenhum contrato ativo sem a modalide selecionada.",
							dataGridConfig: this.getGridContratosAdicionarModalidade(),
						},
					],
				},
				{
					title: "Retirar modalidade em contratos de determinado plano ",
					description:
						'A funcionalidade "Retirar modalidade em contratos de determinado plano" permite que o usuário remova uma modalidade específica de contratos vinculados a um plano determinado. Essa ação pode ser útil quando um cliente deseja fazer uma alteração em seu plano ou quando é necessário ajustar os serviços associados a determinados contratos',
					type: ConfigItemType.GROUP,
					children: [
						{
							title: "Empresa",
							codigo: false,
							type: ConfigItemType.SELECTFILTER,
							endpointUrl:
								this.discoveryService.getUrlMap().admMsUrl +
								"/v1/empresa/resumo",
							placeholder: "Pesquise uma empresa",
							labelKey: "nome",
							idKey: "codigo",
							formControl: this.formControlEmpresaRemoverModalidade,
							responseParser: (result: any) => result.content,
							paramBuilder: (nome: string) => {
								return {
									page: "0",
									size: "20",
									nome,
									orderBy: "nome",
								};
							},
						},
						{
							title: "Modalidade",
							codigo: false,
							type: ConfigItemType.SELECTFILTER,
							endpointUrl:
								this.discoveryService.getUrlMap().admMsUrl + "/v1/modalidade",
							placeholder: "Pesquise uma modalidade",
							labelKey: "nome",
							idKey: "codigo",
							formControl: this.formControlModalidadeRemoverModalidade,
							responseParser: (result: any) => result.content,
							paramBuilder: (nome: string) => {
								return {
									page: "0",
									size: "20",
									nome,
									orderBy: "nome",
								};
							},
						},
						{
							title: "Plano",
							codigo: false,
							type: ConfigItemType.SELECTFILTER,
							endpointUrl:
								this.discoveryService.getUrlMap().admMsUrl + "/v1/plano",
							placeholder: "Pesquise um plano",
							labelKey: "descricao",
							idKey: "codigo",
							formControl: this.formControlPlanoRemoverModalidade,
							responseParser: (result: any) => result.content,
							paramBuilder: (nome: string) => {
								return {
									page: "0",
									size: "20",
									nome,
									orderBy: "descricao",
								};
							},
						},
						{
							title: "Buscar contratos",
							typeDescription: "Buscar contratos",
							type: ConfigItemType.BUTTON,
							description:
								'A funcionalidade "Buscar contratos" permite que o usuário  localize os contratos que possuem a modalidade específica no qual deseja retirar  do plano selecionado, em seguida, realizar ações específicas nesses contratos. É uma ferramenta útil para gerenciar contratos de clientes de acordo com suas necessidades e preferências',
							codigo: false,
							disabled: () => {
								return (
									this.formControlEmpresaRemoverModalidade.value === null ||
									this.formControlModalidadeRemoverModalidade.value === null ||
									this.formControlPlanoRemoverModalidade.value === null
								);
							},
							onClick: () => {
								this.consultarContratosParaRemoverModalidade();
							},
						},
						{
							title: "Remover modalidade aos contratos encontrados",
							typeDescription: "Remover modalidade",
							type: ConfigItemType.BUTTON,
							description:
								'A funcionalidade "Remover modalidade aos contratos encontrados" permite ao usuário remover uma modalidade específica de contratos encontrados que possuem essa modalidade. Isso é útil para ajustar os serviços ou planos dos clientes quando não desejam mais essa modalidade em seus contratos',
							codigo: false,
							disabled: () =>
								this.formControlDataGridContratosRemoverModalidade.value ===
									null ||
								this.formControlDataGridContratosRemoverModalidade.value ===
									undefined ||
								(Array.isArray(
									this.formControlDataGridContratosRemoverModalidade.value
										.content
								) &&
									this.formControlDataGridContratosRemoverModalidade.value
										.content.length === 0),
							onClick: () => {
								this.zwApiServeletProcessos
									.removerModalidadeEmContratos({
										chave: this.sessionService.chave,
										plano: this.formControlPlanoRemoverModalidade.value.codigo,
										empresa:
											this.formControlEmpresaRemoverModalidade.value.codigo,
										modalidade:
											this.formControlModalidadeRemoverModalidade.value.codigo,
										usuario: this.sessionService.loggedUser.id,
										descricaoHorario: this
											.formControlDataGridContratosRemoverModalidade.value
											? this.formControlDataGridContratosRemoverModalidade.value
													.descricao
											: null,
									})
									.subscribe((msg) => {
										this.notificationService.success(msg);
										this.consultarContratosParaRemoverModalidade();
									});
							},
						},
						{
							title: "Lista de contratos de onde serão removidas a modalidades",
							type: ConfigItemType.GRID,
							description:
								"Tem o objetivo de listar os contratos dos quais o usuário deseja remover uma modalidade específica. Isso é útil quando você precisa desvincular contratos de modalidades para que os clientes não tenham mais acesso aos serviços ou benefícios relacionados a essa modalidade",
							codigo: false,
							hide: () =>
								this.formControlDataGridContratosRemoverModalidade.value ===
								null,
							emptyStateMessage:
								"Não existe nenhum contrato ativo com a modalide e plano selecionado.",
							dataGridConfig: this.getGridContratosRemoverModalidade(),
						},
					],
				},
				{
					title: "Corrigir férias dos contratos",
					type: ConfigItemType.GROUP,
					description:
						"Adicionar férias para clientes que compraram contrato antes de ter uma férias cadastrada no plano. " +
						"Obs.: Todos os contratos do banco de dados serão alterados. " +
						"Desc.: Técnica:	Altera a quantidade de dias de férias dos contratos relativos ao Plano informado, ativos ou não, que ainda não tiverem este Ajuste Manual.",
					children: [
						{
							title: "Plano",
							codigo: false,
							type: ConfigItemType.SELECTFILTER,
							description:
								" Permite selecionar um plano específico ao realizar o processo de correção das férias. Isso é útil quando deseja aplicar ajustes ou modificações apenas a um plano de férias específico, em vez de afetar todos os planos de férias disponíveis",
							endpointUrl:
								this.discoveryService.getUrlMap().admMsUrl + "/v1/plano",
							placeholder: "Pesquise um plano",
							labelKey: "descricao",
							idKey: "codigo",
							formControl: this.formControlPlanoCorrigirFeriasContratos,
							responseParser: (result: any) => result.content,
							paramBuilder: (nome: string) => {
								return {
									page: "0",
									size: "20",
									nome,
									orderBy: "descricao",
								};
							},
						},
						{
							title: "Verificar quantidade de contratos",
							typeDescription: "Verificar quantidade de contratos",
							description: `Permite pesquisar a quantidade de contratos que têm a quantidade de dias de férias diferente da quantidade estipulada pelo plano. Isso é útil para identificar contratos que podem ter sido configurados incorretamente ou que precisam ser ajustados para corresponder às configurações do plano de férias`,
							type: ConfigItemType.BUTTON,
							codigo: false,
							onClick: () => {
								this.zwApiServeletProcessos
									.consultarTotalContratosPlano(
										this.sessionService.chave,
										this.formControlPlanoCorrigirFeriasContratos.value.codigo
									)
									.subscribe((total: number) => {
										this.formControlTotalContratosCorrigirFerias.setValue(
											total
										);
									});
							},
						},
						{
							title: "Corrigir contratos",
							typeDescription: "Corrigir contratos",
							codigo: false,
							disabled: () =>
								this.formControlTotalContratosCorrigirFerias.value === 0 ||
								this.formControlTotalContratosCorrigirFerias.value === null,
							description: () => this.btnDescriptionCorrigirFeriasContratos(),
							type: ConfigItemType.BUTTON,
							onClick: () => {
								this.zwApiServeletProcessos
									.corrigirFeriasContratos({
										chave: this.sessionService.chave,
										plano:
											this.formControlPlanoCorrigirFeriasContratos.value.codigo,
									})
									.subscribe((msg) => {
										this.notificationService.success(msg);
										this.formControlTotalContratosCorrigirFerias.setValue(null);
									});
							},
						},
					],
				},
				{
					title: "Corrigir itens da meta diaria que foram duplicados CRM",
					type: ConfigItemType.GROUP,
					description:
						"Objetivo:	Por alguma falha no robo os itens da meta acabaram sendo duplicados ou triplicados, este processo ajusta mantendo apenas os itens corretos da meta." +
						"Observação:	Caso o item da meta esteja triplicado ou quadruplicado, é necessario que o processo seja executado mais vezes, Ex: caso triplicado, 2 vezes, caso quadruplicado, 3 vezes. Etc...",
					children: [
						{
							title: "Iniciar processo a partir de",
							formControl: this.fromControlCorrigirMetaDiariaDuplicados,
							description:
								"Permite selecionar a data da meta diária a partir da qual deseja iniciar um processo ou correção. Isso é útil quando você deseja realizar ações em um conjunto específico de metas diárias a partir de uma determinada data",

							codigo: false,
							dateFilter: this.dateFilterMenorHoje,
							type: ConfigItemType.DATE,
						},
						{
							title: "Corrigir itens duplicados",
							typeDescription: "Corrigir",
							description:
								"Permite corrigir itens duplicados na meta diária do CRM. Isso pode ser útil quando ocorre a duplicação de registros ou metas no sistema, e você deseja limpar essas duplicidade  para manter seus dados organizados e precisos",
							codigo: false,
							type: ConfigItemType.BUTTON,
							onClick: () => {
								const data = new Date(
									this.fromControlCorrigirMetaDiariaDuplicados.value
								);
								this.zwApiServeletProcessos
									.corrigirItensmetaDiariaDuplicados({
										chave: this.sessionService.chave,
										dataInicio: data.toLocaleDateString("pt-BR"),
									})
									.subscribe((msg) => {
										this.notificationService.success(msg);
									});
							},
						},
					],
				},
				{
					title: "Processar pagamento de PIX",
					description:
						"Realizar pagamento de um pix gerado e que ainda está como AGUARDANDO PAGAMENTO",
					type: ConfigItemType.GROUP,
					children: [
						{
							title: "Códigos do Pix",
							formControl: this.formControlCodigosPix,
							description:
								"Para realizar o pagamento usando o código do Pix, você deve informar o código do Pix correto da transação que deseja efetuar. Se você estiver pagando várias transações de uma só vez, separe os códigos dos Pix desejados por VIRGULAS. Isso permite que o sistema identifique cada transação individualmente e processe os pagamentos de acordo",
							type: ConfigItemType.TEXT,
							maxlength: 300,
							codigo: false,
							textMask: new Array(300).fill(/^[\d,]*$/),
						},
						{
							title: "Data de pagamento",
							formControl: this.formControlPixDataPagamento,
							description:
								"Permite que você insira a data de pagamento desejada para o processo que está aguardando pagamento. Essa funcionalidade é útil quando você precisa definir uma data específica para o pagamento de algum processo ou transação no sistema",
							type: ConfigItemType.DATE,
						},
						{
							title: "Processar pagamentos dos ultimos 5 minutos",
							typeDescription: "Processar",
							description:
								"Irá realizar o pagamento, seguindo todo o fluxo de baixa normalmente, como mudar a situação da parcela para PAGA, situação do pix para PAGO, gerar recibo, etc.Qualquer Pix que estava aguardando pagamento é cancelado na produção, pois o pagamento foi processado com sucesso",
							codigo: false,
							disabled: () => {
								return (
									!this.formControlCodigosPix.value ||
									this.formControlCodigosPix.value.trim() === "" ||
									!this.formControlPixDataPagamento.value
								);
							},
							type: ConfigItemType.BUTTON,
							onClick: () => {
								this.zwApiServeletProcessos
									.processarPagamentoPix({
										chave: this.sessionService.chave,
										dataPagamento:
											this.formControlPixDataPagamento.value.toLocaleDateString(
												"pt-BR"
											),
										codigosPix: this.formControlCodigosPix.value,
									})
									.subscribe((res: ProcessarPagamentoPixVO) => {
										if (res.processados && res.processados.length > 0) {
											this.notificationService.success(
												`Foram processados ${res.processados.length} pix.`
											);
											const dadosProcessados = res.processados.map((codigo) => {
												return { codigo };
											});
											this.formControlDataGridPixProcessados.setValue({
												content: dadosProcessados,
											});
										} else {
											this.formControlDataGridPixProcessados.setValue({
												content: [],
											});
										}

										if (res.erros && res.erros.length > 0) {
											const dadosErro = res.erros.map((codigo) => {
												return { codigo };
											});
											this.formControlDataGridPixNaoProcessados.setValue({
												content: dadosErro,
											});
											this.notificationService.error(
												`Não foi possível processar ${res.erros.length} pix.`
											);
										} else {
											this.formControlDataGridPixNaoProcessados.setValue({
												content: [],
											});
										}
									});
							},
						},
						{
							title: "Pix processados",
							type: ConfigItemType.GRID,
							codigo: false,
							hide: () =>
								!this.formControlDataGridPixProcessados.value ||
								this.formControlDataGridPixProcessados.value.content.length ===
									0,
							dataGridConfig: this.getGridPixProcessados(),
						},
						{
							title: "Pix não processados",
							type: ConfigItemType.GRID,
							codigo: false,
							hide: () =>
								!this.formControlDataGridPixNaoProcessados.value ||
								this.formControlDataGridPixNaoProcessados.value.content
									.length === 0,
							dataGridConfig: this.getGridPixNaoProcessados(),
						},
					],
				},
				{
					title: "Excluir Cliente",
					type: ConfigItemType.GROUP,
					codigo: false,
					hide: () => !this.permissaoService.temPermissaoAdm("9.69"),
					description: `Excluir cliente do ZW e Treino. Obs.: Todos os dados relacionados ao cliente serão excluídos, 
								inclusive pagamentos, o que acarretará inconsistências nos relatórios financeiros.`,
					children: [
						{
							title: "Exclui os dados das tabelas abaixo :",
							description: `
								<hr></hr>
								<p><b>Passivo</b></p>
								<p><b>Indicação</b></p>
								<p><b>Atestado</b></p>
								<p><b>Arquivo</b></p>
								<p><b>Liberação de Acesso</b></p>
								<p><b>CaixaMovConta (Movimentação no Financeiro)</b></p>
								<p><b>MovConta (Movimentação no Financeiro)</b></p>
								<p><b>MovContaContabil</b></p>
								<p><b>Mala Direta Enviada</b></p>
								<p><b>Mala Direta Crm Extra</b></p>
								<p><b>Fechar Meta Detalhado</b></p>
								<p><b>Histórico Contato</b></p>
								<p><b>Agenda do CRM (Agendamento Contato)</b></p>
								<p><b>Reposição de Aula</b></p>
								<p><b>Situacao Cliente Sintético</b></p>
								<p><b>NFSe Emitida (Não excluirá caso seja colaborador)</b></p>
								<p><b>CartaoCredito (Parcelas Cartão Crédito - Gestão Recebíveis)</b></p>
								<p><b>Recibo Cliente Consultor</b></p>
								<p><b>Itens de Remessa (Não excluirá caso seja colaborador)</b></p>
								<p><b>Itens de Remessa (MovParcela) (Não excluirá caso seja colaborador)</b></p>
								<p><b>Periodo de Acesso Cliente</b></p>
								<p><b>Contrato</b></p>
								<p><b>Movimento Produto</b></p>
								<p><b>Risco</b></p>
								<p><b>Aula Avulsa ou Diária</b></p>
								<p><b>Taxa de Personal (Item)</b></p>
								<p><b>Aluno Horário Turma</b></p>
								<p><b>Sorteio</b></p>
								<p><b>Cliente</b></p>
								<p><b>Movimento de Pagamento (Quitação) (Não excluirá caso seja colaborador)</b></p>
								<p><b>Pessoa (Não excluirá caso seja colaborador)</b></p>
								<hr></hr>
							`,
							codigo: false,
							type: ConfigItemType.GROUP,
						},
						{
							title: "Matricula Cliente",
							codigo: false,
							type: ConfigItemType.TEXT,
							placeholder: "Infome uma Matricula",
							formControl: this.formControlPesquisarMatricula,
						},
						{
							title: "Verificar Matricula",
							typeDescription: "Verificar Matricula",
							type: ConfigItemType.BUTTON,
							codigo: false,
							description: "Clique verificar matricula do cliente: ",
							onClick: () => this.verificarMatricula(),
						},
						{
							title: "Excluir",
							typeDescription: "Clique no botão para processar",
							type: ConfigItemType.BUTTON,
							codigo: false,
							description: "Clique no botão ao lado para processar: ",
							onClick: () => this.excluirCliente(),
						},
					],
				},
				{
					title: "Excluir Senha de Acesso (Catraca)",
					type: ConfigItemType.GROUP,
					codigo: false,
					hide: () => !this.permissaoService.temPermissaoAdm("9.101"),
					description: `O sistema irá excluir os registros de senha de acesso a catraca.
					 Pode ser selecionado para apagar as senhas de acesso de Alunos ou Colaboradores.`,
					children: [
						{
							title: "Excluir senhas de Colaboradores: ",
							type: ConfigItemType.CHECKBOX,
							codigo: false,
							description:
								"Selecione ao menos um tipo de senha para ser excluída.",
							formControl: this.formControlExcluirColaboradores,
						},
						{
							title: "Excluir senhas de Alunos: ",
							type: ConfigItemType.CHECKBOX,
							codigo: false,
							description:
								"Selecione ao menos um tipo de senha para ser excluída.",
							formControl: this.formControlExcluirAlunos,
						},
						{
							title: "Excluir",
							typeDescription: "Clique no botão para processar",
							type: ConfigItemType.BUTTON,
							codigo: false,
							description: "Clique no botão ao lado para processar: ",
							onClick: () => this.excluirAlunosCatrata(),
						},
					],
				},
			],
		};
	}

	getGridPixNaoProcessados(): PactoDataGridConfig {
		return new PactoDataGridConfig({
			pagination: false,
			dataAdapterFn: () => {
				return {
					content: [],
				};
			},
			dataFormControl: this.formControlDataGridPixNaoProcessados,
			columns: [
				{
					nome: "codigo",
					titulo: "Código do pix",
					visible: true,
					ordenavel: false,
				},
			],
		});
	}

	getGridPixProcessados(): PactoDataGridConfig {
		return new PactoDataGridConfig({
			pagination: false,
			dataAdapterFn: () => {
				return {
					content: [],
				};
			},
			dataFormControl: this.formControlDataGridPixProcessados,
			columns: [
				{
					nome: "codigo",
					titulo: "Código do pix",
					visible: true,
					ordenavel: false,
				},
			],
		});
	}

	btnDescriptionCorrigirFeriasContratos(): string {
		let msg = "Vefifique a quantidade de contratos a corrigir.";

		if (this.formControlTotalContratosCorrigirFerias) {
			if (this.formControlTotalContratosCorrigirFerias.value === null) {
				msg = `Primeiro verifique a quantidade de contratos a corrgir.`;
			}

			if (this.formControlTotalContratosCorrigirFerias.value === 0) {
				msg = `Não existem contratos para o plano ${this.formControlPlanoCorrigirFeriasContratos.value.descricao}`;
			}

			if (this.formControlTotalContratosCorrigirFerias.value > 0) {
				msg = `Corrige ${this.formControlTotalContratosCorrigirFerias.value} os contratos que estão com a quantidade de dias de férias diferente do plano.`;
			}
		}

		return msg;
	}

	getGridContratosRemoverModalidade(): PactoDataGridConfig {
		return new PactoDataGridConfig({
			pagination: false,
			endpointParamsType: "query",
			dataAdapterFn: () => {
				return {
					content: [],
				};
			},
			dataFormControl: this.formControlDataGridContratosRemoverModalidade,
			columns: this.getGridColumnManutencaoModalidade(),
		});
	}

	consultarContratosParaRemoverModalidade() {
		this.zwApiServeletProcessos
			.consultarContratosParaRetirarModalidade(
				this.sessionService.chave,
				this.formControlPlanoRemoverModalidade.value.codigo,
				this.formControlEmpresaRemoverModalidade.value.codigo,
				this.formControlModalidadeRemoverModalidade.value.codigo
			)
			.subscribe((contratos: PlanoContratoVO[]) => {
				this.formControlDataGridContratosRemoverModalidade.setValue({
					content: contratos,
				});
			});
	}

	private consultarContratosParaAdiconarModalidade() {
		this.zwApiServeletProcessos
			.consultarContratosParaAdicionarModalidade(
				this.sessionService.chave,
				this.formControlPlanoAdicionarModalidade.value.codigo,
				this.formControlEmpresaAdicionarModalidade.value.codigo,
				this.formControlModalidadeAdicionarModalidade.value.codigo,
				this.formControlHorarioAdicionarModalidade.value
					? this.formControlHorarioAdicionarModalidade.value.descricao
					: null
			)
			.subscribe((contratos: PlanoContratoVO[]) => {
				this.formControlDataGridContratosAdicionarModalidade.setValue({
					content: contratos,
				});
			});
	}

	getGridContratosAdicionarModalidade(): PactoDataGridConfig {
		return new PactoDataGridConfig({
			pagination: false,
			endpointParamsType: "query",
			dataAdapterFn: () => {
				return {
					content: [],
				};
			},
			dataFormControl: this.formControlDataGridContratosAdicionarModalidade,
			columns: this.getGridColumnManutencaoModalidade(),
		});
	}

	getGridColumnManutencaoModalidade(): PactoDataGridColumnConfigDto[] {
		return [
			{
				nome: "pessoa",
				titulo: "Pessoa",
				visible: true,
				ordenavel: false,
			},
			{
				nome: "contrato",
				titulo: "Contrato",
				visible: true,
				ordenavel: false,
			},
			{
				nome: "plano",
				titulo: "Plano",
				visible: true,
				ordenavel: false,
			},
		];
	}

	dateFilterMenorHoje(date: Date): boolean {
		const current = new Date();
		current.setHours(0, 0, 0, 0);
		return date.getTime() <= current.getTime();
	}

	getInputProcessosManutencaoProdutosParcelas(): SubGrupoInputs {
		const description =
			"Lorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque efficitur nunc sit amet venenatis pellentesque.";

		const temDadosParaCorrgir = (fromControl: FormControl): boolean => {
			return (
				fromControl.value !== null &&
				fromControl.value !== undefined &&
				fromControl.value.content !== null &&
				fromControl.value.content !== undefined &&
				Array.isArray(fromControl.value.content) &&
				fromControl.value.content.length > 0
			);
		};

		return {
			subgrupo: ConfigModuloSubGroup.ADM_MANUTENCAO_PRODUTOS_PARCELAS,
			inputs: [
				{
					title: "Reprocessar Extratos",
					description:
						"Objetivo:	Reprocessar arquivos de extrato a partir de uma determinada data ou por período. " +
						"Observações da Cielo: Quando for reprocessar, fazer em pequenos períodos até o dia atual, pois pode existir cancelamentos em arquivos posteriores à data de processamento inicia. " +
						"Observações da Rede: Precisa dos dados de Venda para conciliar o Recebimento. Por isso, clientes recem configurados não irão conciliar Recebimento corretamente por um tempo.",
					type: ConfigItemType.RANGE,
					children: [
						{
							title: "De",
							formControl: this.fromControlReprocessarExtratosDataIncio,
							type: ConfigItemType.DATE,
							dateFilter: this.dateFilterMenorHoje,
							validators: (
								control: AbstractControl
							): ValidationErrors | null => {
								if (!control.value) {
									return null;
								}

								const dataInicial = new Date(control.value);
								dataInicial.setHours(0, 0, 0, 0);
								const dataAtual = new Date();

								if (dataInicial > dataAtual) {
									return {
										message: "Esta data não pode ser maior do que hoje.",
									};
								}

								if (this.fromControlReprocessarExtratosDataFim.value) {
									const dataFinal = new Date(
										this.fromControlReprocessarExtratosDataFim.value
									);
									dataFinal.setHours(23, 59, 59, 999);
									const diferencaEmMilissegundos =
										dataFinal.getTime() - dataInicial.getTime();
									const umDiaEmMilissegundos = 24 * 60 * 60 * 1000;
									const diferencaEmDias =
										diferencaEmMilissegundos / umDiaEmMilissegundos;

									if (diferencaEmDias > 31) {
										return {
											message:
												"O intervalo máximo permitido para reprocessamento é de 31 dias.",
										};
									}
								}

								return null;
							},
						},
						{
							title: "Até",
							formControl: this.fromControlReprocessarExtratosDataFim,
							type: ConfigItemType.DATE,
							dateFilter: this.dateFilterMenorHoje,
							validators: (
								control: AbstractControl
							): ValidationErrors | null => {
								if (!control.value) {
									return null;
								}

								const dataFinal = new Date(control.value);
								const dataAtual = new Date();

								if (dataFinal > dataAtual) {
									return {
										message: "Esta data não pode ser maior do que hoje.",
									};
								}

								if (this.fromControlReprocessarExtratosDataIncio.value) {
									const dataInicio = new Date(
										this.fromControlReprocessarExtratosDataIncio.value
									);
									dataFinal.setHours(23, 59, 59, 999);
									dataInicio.setHours(0, 0, 0, 0);

									if (dataInicio > dataFinal) {
										return {
											message:
												"A data de inicio está maior do que a data final.",
										};
									}

									const diferencaEmMilissegundos =
										dataFinal.getTime() - dataInicio.getTime();
									const umDiaEmMilissegundos = 24 * 60 * 60 * 1000;
									const diferencaEmDias =
										diferencaEmMilissegundos / umDiaEmMilissegundos;

									if (diferencaEmDias > 31) {
										return {
											message:
												"O intervalo máximo permitido para reprocessamento é de 31 dias.",
										};
									}
								}

								return null;
							},
						},
						{
							title: "Reprocessar",
							typeDescription: "Reprocessar",
							disabled: () => {
								return (
									!(
										this.fromControlReprocessarExtratosDataIncio.valid &&
										this.fromControlReprocessarExtratosDataFim.valid
									) || this.loading
								);
							},
							onClick: () => {
								const dataInicio = new Intl.DateTimeFormat("pt-BR").format(
									this.fromControlReprocessarExtratosDataIncio.value
								);
								const dataFim = new Intl.DateTimeFormat("pt-BR").format(
									this.fromControlReprocessarExtratosDataIncio.value
								);
								this.loading = true;
								this.zwApiServeletProcessos
									.reprocessarExtratos({
										dataInicio,
										dataFim,
										chave: this.sessionService.chave,
									})
									.subscribe({
										next: (res) => {
											this.loading = false;
											if (res.content) {
												this.notificationService.success(res.content);
											} else if (res.meta.error) {
												this.notificationService.error(res.meta.message);
											}
											this.detectChanges.emit(true);
										},
										error: (error) => {
											this.loading = false;
											if (
												error.error &&
												error.error.meta &&
												error.error.meta.message
											) {
												this.notificationService.error(
													error.error.meta.message
												);
											} else {
												this.notificationService.error(
													"Não foi possível reprocessar os extratos devido a um erro desconhecido!"
												);
											}
											this.detectChanges.emit(true);
										},
									});
							},
							type: ConfigItemType.BUTTON,
						},
					],
				},
				{
					title: "Pagamentos com parcela sem recibo",
					description:
						'A ação "Pagamentos com parcela sem recibo" tem o objetivo de corrigir pagamentos associados a parcelas que estão sem recibo. Ela pode ser útil para manter a integridade dos registros financeiros e garantir que todas as transações de pagamento estejam adequadamente documentadas',
					type: ConfigItemType.GROUP,
					children: [
						{
							title: "Verificar pagamentos com parcela sem recibo",
							typeDescription: "Verificar",
							type: ConfigItemType.BUTTON,
							description:
								'A ação "Verificar pagamentos com parcela sem recibo" tem como objetivo identificar pagamentos associados a parcelas que não possuem um recibo correspondente. Isso pode ocorrer devido a erros no registro de transações ou a problemas no processo de geração de recibos',
							codigo: false,
							onClick: () => {
								this.zwApiServeletProcessos
									.consultarPagamentoParcelasSemRecibo(
										this.sessionService.chave
									)
									.subscribe((data: ProdutosParcelas[]) => {
										this.formControlDataGridPagamentosSemRecibo.setValue({
											content: data,
										});
										if (data.length === 0) {
											this.notificationService.info(
												"Não foram encontrados pagamentos com parcelas sem recibo."
											);
										}
									});
							},
						},
						{
							title: "Corrigir pagamentos com parcela sem recibo",
							typeDescription: "Corrigir",
							type: ConfigItemType.BUTTON,
							description:
								'A ação "Corrigir pagamentos com parcela sem recibo" tem como objetivo resolver situações em que os registros de pagamento foram feitos para parcelas, mas não há um recibo associado a esses pagamentos. Isso pode ocorrer por erros de entrada de dados, falhas no processo de geração de recibos ou outras razões',
							codigo: false,
							disabled: () =>
								!temDadosParaCorrgir(
									this.formControlDataGridPagamentosSemRecibo
								),
							onClick: () => {
								this.zwApiServeletProcessos
									.corrigirPagamentoParcelasSemRecibo(this.sessionService.chave)
									.subscribe((msg) => {
										this.notificationService.success(msg);
										this.zwApiServeletProcessos
											.consultarPagamentoParcelasSemRecibo(
												this.sessionService.chave
											)
											.subscribe((data: ProdutosParcelas[]) => {
												this.formControlDataGridPagamentosSemRecibo.setValue({
													content: data,
												});
											});
									});
							},
						},
						{
							title: "Lista de pagamentos com parcelas sem recibo a corrigir",
							type: ConfigItemType.GRID,
							codigo: false,
							hide: () =>
								!temDadosParaCorrgir(
									this.formControlDataGridPagamentosSemRecibo
								),
							dataGridConfig: this.getGridProcessosManutencao(
								this.formControlDataGridPagamentosSemRecibo
							),
							emptyStateMessage:
								"Clique em verificar para consultar se existem inconsistências.",
						},
					],
				},
				{
					title: "Pagamentos sem produtos pagos",
					type: ConfigItemType.GROUP,
					description:
						'A ação "Pagamentos sem produtos pagos" tem como objetivo identificar pagamentos em que não houve associação a produtos ou serviços específicos,  para que seja possível acompanhar as transações e receitas de forma precisa.O sistema verifica todos os pagamentos que não estão vinculados a produtos ou serviços específicos',
					children: [
						{
							title: "Verificar pagamentos sem produtos pagos",
							typeDescription: "Verificar",
							description:
								'A ação "Verificar pagamentos sem produtos pagos" tem como objetivo identificar pagamentos em que não houve associação a produtos ou serviços específicos. Essa verificação é importante para garantir que todos os registros financeiros estejam completos e precisos, além de ajudar na rastreabilidade das transações',
							codigo: false,
							type: ConfigItemType.BUTTON,
							onClick: () => {
								this.zwApiServeletProcessos
									.consultarPagamentoSemProdutoPago(this.sessionService.chave)
									.subscribe((data: ProdutosParcelas[]) => {
										this.formControlDataGridPagamentoSemProdutosPagos.setValue({
											content: data,
										});
										if (data.length === 0) {
											this.notificationService.info(
												"Não foram encontrados pagamentos sem produtos pagos."
											);
										}
									});
							},
						},
						{
							title: "Corrigir pagamentos sem produtos pagos",
							typeDescription: "Corrigir",
							description:
								'A ação "Corrigir pagamentos sem produtos pagos" tem como objetivo corrigir pagamentos registrados no sistema que não estão associados a produtos ou serviços específicos. Essa correção é importante para garantir a precisão e a completude dos registros financeiros da organização',
							codigo: false,
							type: ConfigItemType.BUTTON,
							disabled: () =>
								!temDadosParaCorrgir(
									this.formControlDataGridPagamentoSemProdutosPagos
								),
							onClick: () => {
								this.zwApiServeletProcessos
									.corrigirPagamentoSemProdutoPago(this.sessionService.chave)
									.subscribe((msg) => {
										this.notificationService.success(msg);
										this.zwApiServeletProcessos
											.consultarPagamentoSemProdutoPago(
												this.sessionService.chave
											)
											.subscribe((data: ProdutosParcelas[]) => {
												this.formControlDataGridPagamentoSemProdutosPagos.setValue(
													{ content: data }
												);
											});
									});
							},
						},
						{
							title: "Lista de pagamentos sem produtos pagos",
							type: ConfigItemType.GRID,
							description:
								'A funcionalidade "Lista de pagamentos sem produtos pagos" permite listar os clientes com pagamentos que não incluem produtos pagos. Isso pode ser útil para identificar e corrigir quaisquer pagamentos que não estejam refletindo corretamente a compra de produtos',
							codigo: false,
							hide: () =>
								!temDadosParaCorrgir(
									this.formControlDataGridPagamentoSemProdutosPagos
								),
							dataGridConfig: this.getGridProcessosManutencao(
								this.formControlDataGridPagamentoSemProdutosPagos
							),
							emptyStateMessage:
								"Clique em verificar para consultar se existem inconsistências.",
						},
					],
				},
				{
					title: "Pagamentos sem vínculos com parcelas",
					type: ConfigItemType.GROUP,
					children: [
						{
							title: "Verificar pagamentos sem vínculos com parcelas",
							typeDescription: "Verificar",
							description:
								'A funcionalidade "Pagamentos sem vínculos com parcelas" tem como objetivo identificar os pagamentos que não possuem vínculos com parcelas no sistema. Isso pode ocorrer quando um pagamento é registrado, mas não está associado a nenhuma parcela específica. A correção desses pagamentos pode ser necessária para garantir que os registros contábeis estejam corretos e que os clientes tenham seus pagamentos devidamente registrados',
							codigo: false,
							type: ConfigItemType.BUTTON,
							onClick: () => {
								this.zwApiServeletProcessos
									.consultarPagamentoSemVinculoParcela(
										this.sessionService.chave
									)
									.subscribe((data: ProdutosParcelas[]) => {
										this.formControlDataGridPagamentosSemVinculoParcelas.setValue(
											{ content: data }
										);
										if (data.length === 0) {
											this.notificationService.info(
												"Não foram encontrados pagamentos sem vínculos com parcelas."
											);
										}
									});
							},
						},
						{
							title: "Lista de pagamentos sem vínculos com parcelas",
							type: ConfigItemType.GRID,
							description:
								"A correção deste item deve ser feita de forma manual, visto que será necessária uma análise individual de cada caso",
							codigo: false,
							hide: () =>
								!temDadosParaCorrgir(
									this.formControlDataGridPagamentosSemVinculoParcelas
								),
							dataGridConfig: this.getGridProcessosManutencao(
								this.formControlDataGridPagamentosSemVinculoParcelas
							),
							emptyStateMessage:
								"Clique em verificar para consultar se existem inconsistências.",
						},
					],
				},
				{
					title: "Parcelas sem produtos",
					type: ConfigItemType.GROUP,
					description:
						'A funcionalidade "Parcelas sem produtos" tem como objetivo identificar as parcelas que não possuem produtos associados no sistema. Isso pode ocorrer quando as parcelas foram registradas, mas não têm produtos ou serviços vinculados a elas',
					children: [
						{
							title: "Verificar Parcelas sem produtos",
							typeDescription: "Verificar",
							description:
								"Tem o objetivo de identificar as parcelas no sistema que não possuem produtos ou serviços associados a elas",
							codigo: false,
							type: ConfigItemType.BUTTON,
							onClick: () => {
								this.zwApiServeletProcessos
									.consultarParcelasSemProdutos(this.sessionService.chave)
									.subscribe((data: ProdutosParcelas[]) => {
										this.formControlDataGridParcelasSemProdutos.setValue({
											content: data,
										});
										if (data.length === 0) {
											this.notificationService.info(
												"Não foram encontrados parcelas sem produtos."
											);
										}
									});
							},
						},
						{
							title: "Corrigir parcelas sem produtos",
							typeDescription: "Corrigir",
							description:
								"Tem como objetivo corrigir as parcelas que foram identificadas como não tendo produtos ou serviços associados a elas. Isso é importante para garantir que as informações estejam completas e precisas, evitando discrepâncias nos registros contábeis e mantendo a integridade dos dados relacionados aos serviços ou produtos adquiridos pelos clientes",
							codigo: false,
							type: ConfigItemType.BUTTON,
							disabled: () =>
								!temDadosParaCorrgir(
									this.formControlDataGridParcelasSemProdutos
								),
							onClick: () => {
								this.zwApiServeletProcessos
									.corrigirParcelasSemProdutos(this.sessionService.chave)
									.subscribe((msg) => {
										this.notificationService.success(msg);
										this.zwApiServeletProcessos
											.consultarParcelasSemProdutos(this.sessionService.chave)
											.subscribe((data: ProdutosParcelas[]) => {
												this.formControlDataGridParcelasSemProdutos.setValue({
													content: data,
												});
											});
									});
							},
						},
						{
							title: "Lista de parcelas sem produtos",
							type: ConfigItemType.GRID,
							description,
							codigo: false,
							hide: () =>
								!temDadosParaCorrgir(
									this.formControlDataGridParcelasSemProdutos
								),
							dataGridConfig: this.getGridProcessosManutencao(
								this.formControlDataGridParcelasSemProdutos
							),
							emptyStateMessage:
								"Clique em verificar para consultar se existem inconsistências.",
						},
					],
				},
				{
					title: "Produtos sem parcelas",
					type: ConfigItemType.GROUP,
					description:
						'A funcionalidade "Produtos sem parcelas" tem como objetivo identificar os produtos que não possuem parcelas associadas a eles no sistema. Isso é importante para garantir que todos os produtos comercializados pela empresa estejam corretamente registrados e que as informações estejam completas e precisas',
					children: [
						{
							title: "Verificar produtos sem parcelas",
							typeDescription: "Verificar",
							description:
								"Tem como objetivo verificar a existência de produtos que não possuem parcelas associadas a eles no sistema. Isso é importante para identificar quais produtos podem estar sem informações de pagamento ou parcelamento, o que pode afetar o controle financeiro da empresa",
							codigo: false,
							type: ConfigItemType.BUTTON,
							onClick: () => {
								this.zwApiServeletProcessos
									.consultarProdutosSemParcelas(this.sessionService.chave)
									.subscribe((data: ProdutosParcelas[]) => {
										this.formControlDataGridProdutosSemParcelas.setValue({
											content: data,
										});
										if (data.length === 0) {
											this.notificationService.info(
												"Não foram encontrados produtos sem parcelas."
											);
										}
									});
							},
						},
						{
							title: "Corrigir produtos sem parcelas",
							typeDescription: "Corrigir",
							description:
								"Tem como objetivo corrigir a situação em que existem produtos cadastrados no sistema que não possuem parcelas associadas a eles. Isso é importante para garantir que todos os produtos da empresa estejam devidamente configurados com as informações de pagamento e parcelamento corretas",
							codigo: false,
							type: ConfigItemType.BUTTON,
							disabled: () =>
								!temDadosParaCorrgir(
									this.formControlDataGridProdutosSemParcelas
								),
							onClick: () => {
								this.zwApiServeletProcessos
									.corrigirProdutosSemParcelas(this.sessionService.chave)
									.subscribe((msg) => {
										this.notificationService.success(msg);
										this.zwApiServeletProcessos
											.consultarProdutosSemParcelas(this.sessionService.chave)
											.subscribe((data: ProdutosParcelas[]) => {
												this.formControlDataGridProdutosSemParcelas.setValue({
													content: data,
												});
											});
									});
							},
						},
						{
							title: "Lista de parcelas sem produtos",
							type: ConfigItemType.GRID,
							description:
								"Tem como objetivo apresentar a lista de produtos que estão cadastrados no sistema, mas que não possuem parcelas associadas a eles. Isso é importante para identificar quais produtos podem estar incompletos em termos de configuração de pagamento e parcelamento",
							codigo: false,
							hide: () =>
								!temDadosParaCorrgir(
									this.formControlDataGridProdutosSemParcelas
								),
							dataGridConfig: this.getGridProcessosManutencao(
								this.formControlDataGridProdutosSemParcelas
							),
							emptyStateMessage:
								"Clique em verificar para consultar se existem inconsistências.",
						},
					],
				},
				{
					title: "Parcelas em aberto com pagamento",
					type: ConfigItemType.GROUP,
					description:
						'A funcionalidade "Parcelas em aberto com pagamento" tem como objetivo identificar as parcelas que estão registradas como em aberto, mas que possuem algum registro de pagamento associado a elas. Isso pode ocorrer quando um pagamento foi registrado, mas a parcela correspondente não foi devidamente baixada no sistema, resultando em uma inconsistência nos registros financeiros',
					children: [
						{
							title: "Verificar parcelas em aberto com pagamento",
							typeDescription: "Verificar",
							description:
								"Tem como objetivo identificar as parcelas que estão registradas como em aberto no sistema, mas que possuem registros de pagamento associados a elas",
							codigo: false,
							type: ConfigItemType.BUTTON,
							onClick: () => {
								this.zwApiServeletProcessos
									.consultarParcelasEmAbertoComPagamento(
										this.sessionService.chave
									)
									.subscribe((data: ProdutosParcelas[]) => {
										this.formControlDataGridParcelasEmAbertoComPagamento.setValue(
											{ content: data }
										);
										if (data.length === 0) {
											this.notificationService.info(
												"Não foram encontrados parcelas em aberto com pagamento"
											);
										}
									});
							},
						},
						{
							title: "Corrigir parcelas em aberto com pagamento",
							typeDescription: "Corrigir",
							description:
								"Tem como objetivo corrigir parcelas que estão registradas como em aberto no sistema, mas que possuem registros de pagamento associados a elas. Essa situação pode indicar erros ou inconsistências nos registros financeiros, onde um pagamento foi registrado, mas a parcela correspondente não foi corretamente baixada no sistema",
							codigo: false,
							type: ConfigItemType.BUTTON,
							disabled: () =>
								!temDadosParaCorrgir(
									this.formControlDataGridParcelasEmAbertoComPagamento
								),
							onClick: () => {
								this.zwApiServeletProcessos
									.corrigirParcelasEmAbertoComPagamento(
										this.sessionService.chave
									)
									.subscribe((msg) => {
										this.notificationService.success(msg);
										this.zwApiServeletProcessos
											.consultarParcelasEmAbertoComPagamento(
												this.sessionService.chave
											)
											.subscribe((data: ProdutosParcelas[]) => {
												this.formControlDataGridParcelasEmAbertoComPagamento.setValue(
													{ content: data }
												);
											});
									});
							},
						},
						{
							title: "Lista parcelas em aberto com pagamento",
							type: ConfigItemType.GRID,
							description,
							codigo: false,
							hide: () =>
								!temDadosParaCorrgir(
									this.formControlDataGridParcelasEmAbertoComPagamento
								),
							dataGridConfig: this.getGridProcessosManutencao(
								this.formControlDataGridParcelasEmAbertoComPagamento
							),
							emptyStateMessage:
								"Clique em verificar para consultar se existem inconsistências.",
						},
					],
				},
				{
					title: "Recibos sem produto conta corrente (CC)",
					type: ConfigItemType.GROUP,
					description:
						'A funcionalidade "Recibos sem produto conta corrente (CC)" tem como objetivo identificar os recibos que não possuem um produto associado na conta corrente. A conta corrente é um registro das transações financeiras de um cliente, incluindo pagamentos, recebimentos, compras e vendas',
					children: [
						{
							title: "Verificar recibos sem produto CC",
							typeDescription: "Verificar",
							description:
								"Tem como objetivo permitir que os usuários identifiquem e localizem os recibos que foram registrados no sistema, mas que não possuem um produto associado na conta corrente (CC)",
							codigo: false,
							type: ConfigItemType.BUTTON,
							onClick: () => {
								this.zwApiServeletProcessos
									.consultarRecibosSemProdutosCc(this.sessionService.chave)
									.subscribe((data: ProdutosParcelas[]) => {
										this.formControlDataGridRecibosSemProdutosCC.setValue({
											content: data,
										});
										if (data.length === 0) {
											this.notificationService.info(
												"Não foram encontrados recibos sem produto CC"
											);
										}
									});
							},
						},
						{
							title: "Corrigir recibos sem produto CC",
							typeDescription: "Corrigir",
							description:
								"Tem como objetivo permitir que o  usuário  corrija os recibos que foram registrados no sistema sem um produto associado na conta corrente (CC)",
							codigo: false,
							type: ConfigItemType.BUTTON,
							disabled: () =>
								!temDadosParaCorrgir(
									this.formControlDataGridRecibosSemProdutosCC
								),
							onClick: () => {
								this.zwApiServeletProcessos
									.corrigirRecibosSemProdutosCc(this.sessionService.chave)
									.subscribe((msg) => {
										this.notificationService.success(msg);
										this.zwApiServeletProcessos
											.consultarRecibosSemProdutosCc(this.sessionService.chave)
											.subscribe((data: ProdutosParcelas[]) => {
												this.formControlDataGridRecibosSemProdutosCC.setValue({
													content: data,
												});
											});
									});
							},
						},
						{
							title: "Lista recibos sem produto CC",
							type: ConfigItemType.GRID,
							description,
							codigo: false,
							hide: () =>
								!temDadosParaCorrgir(
									this.formControlDataGridRecibosSemProdutosCC
								),
							dataGridConfig: this.getGridProcessosManutencao(
								this.formControlDataGridRecibosSemProdutosCC
							),
							emptyStateMessage:
								"Clique em verificar para consultar se existem inconsistências.",
						},
					],
				},
				{
					title: "Substituir Produtos",
					type: ConfigItemType.GROUP,
					description:
						'A funcionalidade "Substituir Produtos" tem como objetivo permitir a substituição de produtos em determinados registros ou transações. Isso pode ser útil quando é necessário atualizar ou alterar os produtos associados a registros financeiros, como parcelas de pagamentos, recibos, ou outros documentos relacionados a produtos ou serviços',
					children: [
						{
							title: "Substituir referências a este código de produto",
							description:
								"Permite inserir o código do produto específico por outro código de produto. O sistema atualizará todas as referências ao código de produto antigo pelo novo código de produto nos registros selecionados",
							type: ConfigItemType.NUMBER,
							validators: [Validators.min(1)],
							formControl: this.formControlProdutoSubstituir,
							codigo: false,
						},
						{
							title: "Substituir por este código de produto",
							description:
								"Permite inserir o novo código de produto que será usado para substituir o código de produto antigo em todos os registros onde a substituição for aplicada",
							type: ConfigItemType.NUMBER,
							validators: [Validators.min(1)],
							formControl: this.formControlProdutoSubstituido,
							codigo: false,
						},
						{
							title: "Deletar?",
							description:
								"Permite excluir o produto que foi substituído. Isso significa que você pode remover permanentemente o produto que não deseja mais no sistema, após ter realizado a substituição por outro produto. Certifique-se de tomar essa ação com cuidado, uma vez que a exclusão de um produto pode ser irreversível e resultar na perda de dados associados a esse produto, como histórico de vendas, registros financeiros, etc",
							type: ConfigItemType.CHECKBOX,
							formControl: this.formControlProdutoSubstituidoDeletar,
							codigo: false,
						},
						{
							title: "Processar",
							typeDescription: "Processar",
							description:
								'É utilizada após definir os detalhes da substituição de códigos de produtos, como o código a ser substituído e o código de substituição. Ao clicar em "Processar," o sistema realizará a substituição nos registros ou transações onde o código de produto deve ser atualizado',
							type: ConfigItemType.BUTTON,
							codigo: false,
							disabled: () => {
								return (
									this.formControlProdutoSubstituir.invalid ||
									this.formControlProdutoSubstituido.invalid ||
									this.formControlProdutoSubstituidoDeletar.invalid
								);
							},
							onClick: () => {
								this.zwApiServeletProcessos
									.substituirProdutos({
										chave: this.sessionService.chave,
										produtoSubstituir: this.formControlProdutoSubstituir.value,
										produtoSubstituido:
											this.formControlProdutoSubstituido.value,
										deletar: this.formControlProdutoSubstituidoDeletar.value,
									})
									.subscribe((msg) => {
										this.notificationService.success(msg);
									});
							},
						},
					],
				},
				{
					title: "Corrigir valores contrato com recibo e produtos pagos",
					typeDescription: "Corrigir",
					description:
						'A funcionalidade "Corrigir valores contrato com recibo e produtos pagos" tem o objetivo de realizar correções nos valores dos contratos que possuem recibos e produtos pagos. Isso pode ser necessário se houver discrepâncias nos valores registrados nos contratos em relação aos valores dos recibos e produtos pagos',
					type: ConfigItemType.BUTTON,
					onClick: () => {
						this.zwApiServeletProcessos
							.corrigirValoresContratoReciboProdutosPagamentos(
								this.sessionService.chave
							)
							.subscribe((msg) => {
								this.notificationService.success(msg);
							});
					},
				},
			],
		};
	}

	getGridProcessosManutencao(fromControl: FormControl): PactoDataGridConfig {
		return new PactoDataGridConfig({
			pagination: false,
			endpointParamsType: "query",
			dataAdapterFn: () => {
				return {
					content: [],
				};
			},
			dataFormControl: fromControl,
			columns: this.getGridColumnProcessosManutencao(),
		});
	}

	getGridPagamentosSemVinculoParcela(): PactoDataGridConfig {
		return new PactoDataGridConfig({
			pagination: false,
			endpointParamsType: "query",
			dataAdapterFn: () => {
				return {
					content: [],
				};
			},
			dataFormControl: this.formControlDataGridPagamentosSemVinculoParcelas,
			columns: this.getGridColumnProcessosManutencao(),
		});
	}

	getGridPagamentosSemProdutoPago(): PactoDataGridConfig {
		return new PactoDataGridConfig({
			pagination: false,
			endpointParamsType: "query",
			dataAdapterFn: () => {
				return {
					content: [],
				};
			},
			dataFormControl: this.formControlDataGridPagamentoSemProdutosPagos,
			columns: this.getGridColumnProcessosManutencao(),
		});
	}

	private getGridColumnProcessosManutencao(): PactoDataGridColumnConfigDto[] {
		return [
			{
				nome: "recibo",
				titulo: "Código do recibo",
				visible: true,
				ordenavel: false,
			},
			{
				nome: "nome",
				titulo: "Nome do cliente",
				visible: true,
				ordenavel: false,
			},
			{
				nome: "matricula",
				titulo: "Matrícula",
				visible: true,
				ordenavel: false,
			},
		];
	}

	getGridPagamentosSemRecibo(): PactoDataGridConfig {
		return new PactoDataGridConfig({
			pagination: false,
			endpointParamsType: "query",
			dataAdapterFn: () => {
				return {
					content: [],
				};
			},
			dataFormControl: this.formControlDataGridPagamentosSemRecibo,
			columns: this.getGridColumnProcessosManutencao(),
		});
	}

	getInputsProcessosAutomaticos(): SubGrupoInputs {
		return {
			subgrupo: ConfigModuloSubGroup.ADM_PROCESSOS,
			inputs: [
				{
					title: "Robo",
					typeDescription: "Executar",
					type: ConfigItemType.BUTTON,
				},
				{
					title: "Cancelamento Automático",
					typeDescription: "Executar",
					type: ConfigItemType.BUTTON,
				},
				// TODO: Verificar se é realmente necessário tem este processos migrados e criar o restante se necessário.
			],
		};
	}

	getInputsManutencao(): SubGrupoInputs {
		const description =
			"Lorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque efficitur nunc sit amet venenatis pellentesque.";

		return {
			subgrupo: ConfigModuloSubGroup.ADM_MANUTENCAO_PRODUTOS_PARCELAS,
			inputs: [
				{
					title: "Corrigir Matriculas de Alunos",
					typeDescription: "Executar",
					description,
					type: ConfigItemType.BUTTON,
				},
				{
					title: "Verificar Pagamentos com parcela sem recibo",
					typeDescription: "Verificar",
					description,
					type: ConfigItemType.BUTTON,
				},
				{
					title: "Corrigir Pagamentos com parcela sem recibo",
					typeDescription: "Corrigir",
					description,
					type: ConfigItemType.BUTTON,
				},
				{
					title: "Verificar Pagamentos sem produtos pagos",
					typeDescription: "Verificar",
					description,
					type: ConfigItemType.BUTTON,
				},
				{
					title: "Corrigir Pagamentos sem produtos pagos",
					typeDescription: "Corrigir",
					description,
					type: ConfigItemType.BUTTON,
				},
				{
					title: "Verificar Pagamentos sem vínculos com parcelas",
					typeDescription: "Verificar",
					description,
					type: ConfigItemType.BUTTON,
				},
				{
					title: "Verificar Movparcelas sem movprodutos",
					typeDescription: "Verificar",
					description,
					type: ConfigItemType.BUTTON,
				},
				{
					title: "Corrigir Movparcelas sem movprodutos",
					typeDescription: "Corrigir",
					description,
					type: ConfigItemType.BUTTON,
				},
				{
					title: "Verificar Movprodutos sem movparcelas",
					typeDescription: "Verificar",
					description,
					type: ConfigItemType.BUTTON,
				},
				{
					title: "Corrigir Movprodutos sem movparcelas",
					typeDescription: "Corrigir",
					description,
					type: ConfigItemType.BUTTON,
				},
				{
					title: "Verificar Parcelas em aberto com pagamento",
					typeDescription: "Verificar",
					description,
					type: ConfigItemType.BUTTON,
				},
				{
					title: "Corrigir Parcelas em aberto com pagamento",
					typeDescription: "Corrigir",
					description,
					type: ConfigItemType.BUTTON,
				},
				{
					title: "Recibos sem produto CC",
					typeDescription: "Recibos sem produto CC 0%",
					description,
					type: ConfigItemType.BUTTON,
				},
				{
					title: "Substituir Produtos",
					type: ConfigItemType.GROUP,
					children: [
						{
							title: "Referências a este produto",
							description,
							type: ConfigItemType.NUMBER,
						},
						{
							title: "Substituir por este",
							description,
							type: ConfigItemType.NUMBER,
						},
						{
							title: "Deletar?",
							description,
							type: ConfigItemType.CHECKBOX,
						},
						{
							title: "Processar",
							typeDescription: "Processar",
							type: ConfigItemType.BUTTON,
						},
					],
				},
				{
					title: "Corrigir Valores Contrato Recibo String ProdutosPagos ",
					typeDescription: "Corrigir",
					description,
					type: ConfigItemType.BUTTON,
				},
				{
					title: "Corrigir Histórico Vínculo",
					typeDescription: "Restaurar Vínculos Consultor",
					description,
					type: ConfigItemType.BUTTON,
				},
				{
					title: "Corrigir Histórico Vínculo",
					typeDescription: "Restaurar Vínculos de Professores 0%",
					description,
					type: ConfigItemType.BUTTON,
				},
				{
					title: "Manutenção de fase do crm",
					type: ConfigItemType.GROUP,
					children: [
						{
							title: "Fases do Crm",
							description,
							type: ConfigItemType.SELECT,
							options: [
								{
									id: 1,
									label: "Agendamento presenciais",
								},
							],
						},
						{
							title: "Corrigir fase do crm",
							typeDescription: "Executar",
							type: ConfigItemType.BUTTON,
						},
					],
				},
				{
					title: "Alterar situação de um contrato",
					type: ConfigItemType.GROUP,
					children: [
						{
							title: "Contrato",
							description,
							type: ConfigItemType.NUMBER,
						},
						{
							title: "Renovação -> Rematrícula",
							typeDescription: "Executar",
							type: ConfigItemType.BUTTON,
						},
						{
							title: "Rematrícula -> Renovação",
							typeDescription: "Executar",
							type: ConfigItemType.BUTTON,
						},
					],
				},
			],
		};
	}

	getOptionsAliquotaService(): ConfigItemSelectOption[] {
		// TODO: criar endpoint no micro serviço adm-ms e refatorar para buscar de lá
		return [
			{
				id: 24,
				label: "Alíquota de 0,01%",
			},
			{
				id: 1,
				label: "Alíquota de 01%",
			},
		];
	}

	getInputsRecorrencia(): SubGrupoInputs {
		return {
			subgrupo: ConfigModuloSubGroup.ADM_RECORRENCIA,
			inputs: [
				{
					title: "Configuração para Remessa EDI",
					type: ConfigItemType.GROUP,
					children: [
						{
							name: "enviarremessasremotamente",
							title: "Enviar remessas via SFTP",
							description:
								"Essa permissão, quando habilitada, permite que o usuário envie remessas contendo informações de pagamentos através do protocolo SFTP diretamente pelo sistema. O SFTP é um protocolo seguro e amplamente utilizado para transferência de arquivos de forma criptografada pela internet.Essa permissão é essencial em ambientes onde é necessário transmitir informações de pagamentos de forma segura e padronizada para processamento eficiente. Através do SFTP, é possível garantir a integridade e a segurança dos dados de pagamento durante a transferência.",
							type: ConfigItemType.CHECKBOX,
						},
						{
							name: "usarverificadorremessasrejeitadas",
							title: "Ativar verificador de remessas rejeitadas (Getnet EDI)",
							description:
								"Essa permissão, quando habilitada, permite que o sistema ative um verificador específico para remessas rejeitadas provenientes da operadora Getnet EDI . O objetivo é validar e gerenciar remessas que foram rejeitadas pela operadora após o envio.Essa permissão é crucial para assegurar que as remessas de dados de transações financeiras enviadas à operadora Getnet EDI sejam processadas corretamente e sem problemas.",
							type: ConfigItemType.CHECKBOX,
						},
						{
							name: "agruparremessasgetnet",
							title:
								"Agrupar remessas geradas antes de realizar o envio (Getnet EDI)",
							description:
								'A permissão para "Agrupar Remessas Geradas antes de Realizar o Envio (Getnet EDI)" é uma configuração que permite ao usuário agrupar várias remessas de transações financeiras em uma única remessa antes de enviá-las à operadora Getnet EDI. Isso pode ser útil para otimizar o processo de envio e facilitar o controle de transações financeiras.',
							type: ConfigItemType.CHECKBOX,
						},
						{
							name: "agruparremessascartaoedi",
							title: "Agrupar parcelas em remessas EDI por cartão",
							description:
								'A configuração de "Agrupar Parcelas em Remessas EDI por Cartão" permite ao usuário agrupar várias parcelas associadas a um mesmo cartão em uma única remessa Antes de enviar as informações financeiras para a operadora do cartão no formato EDI.Isso é útil quando se deseja otimizar o processo de envio das informações financeiras para a operadora do cartão, reduzindo a quantidade de remessas a serem processadas.',
							type: ConfigItemType.CHECKBOX,
						},
					],
				},
				{
					title: "Configuração para Transação Online",
					type: ConfigItemType.GROUP,
					children: [
						{
							name: "usaaprovafacil",
							title: "Usa AprovaFácil",
							description:
								'Essa permissão permite ao usuário integrar o sistema com a plataforma "Aprova Fácil". Essa integração pode ser utilizada para facilitar e automatizar processos de aprovação ou autorização em uma variedade de contextos, como finanças, negócios ou qualquer outro que exija aprovação.',
							type: ConfigItemType.CHECKBOX,
						},
						{
							name: "urlrecorrencia",
							title: "URL Recorrência (Aprova Fácil)",
							description:
								"Neste campor ser inserido a URL da plataforma Aprova fácil.",
							type: ConfigItemType.TEXT,
						},
					],
				},
				{
					title: "E-mail do responsável da recorrência",
					entityLogName: "emailsrecorrencia",
					type: ConfigItemType.GRIDEDITABLE,
					description:
						"Neste campo é utilizado para inserir o endereço de e-mail da pessoa ou departamento responsável pela gestão e acompanhamento das transações de recorrência.",
					newLineTitle: "Adicionar email",
					dataGridConfig: this.getEmailRecorrenciaDataGridConfig(),
				},
			],
		};
	}

	getEmailRecorrenciaDataGridConfig(): PactoDataGridConfig {
		return new PactoDataGridConfig({
			pagination: false,
			endpointUrl: `${
				this.discoveryService.getUrlMap().admMsUrl
			}/v1/configuracao/recorrencia/email`,
			endpointParamsType: "query",
			formGroup: new FormGroup({
				codigo: new FormControl(),
				email: new FormControl(null, Validators.email),
			}),
			onDeleteFn: (
				row: { email: string; codigo: number },
				data,
				index: number
			): Observable<boolean> => this.onDeleteEmailRecorrencia(row, data, index),
			onAddFn: (
				row: { email: string; codigo: number },
				data,
				index: number
			): Observable<EmailRecorrencia> =>
				this.onAddEmailRecorrencia(row, data, index),
			onEditFn: (
				row: { email: string; codigo: number },
				data,
				index: number
			): Observable<EmailRecorrencia> =>
				this.onEditEmailRecorrencia(row, data, index),
			beforeConfirm: (row, form, data, indexRow) =>
				this.beforeConfirmEmailRecorrencia(row, form, data, indexRow),
			columns: [
				{
					nome: "email",
					titulo: "Email",
					visible: true,
					ordenavel: false,
					editable: true,
					inputType: "text",
				},
				{
					nome: "codigo",
					titulo: "Código",
					visible: false,
					ordenavel: false,
					editable: false,
				},
			],
		});
	}

	beforeConfirmEmailRecorrencia(row, form, data, indexRow) {
		if (!form.get("email").value || form.get("email").value === "") {
			this.notificationService.error("O e-mail deve ser informado.");
			return false;
		}

		if (!form.get("email").valid) {
			this.notificationService.error("E-mail inválido.");
			return false;
		}

		const emails = data
			.map((d) => d.email)
			.filter((e, index) => index !== indexRow);
		if (emails.includes(form.get("email").value)) {
			this.notificationService.error("E-mail já adicionado.");
			return false;
		}

		return true;
	}
	onEditEmailRecorrencia(
		row: { email: string; codigo: number },
		data: any,
		index: number
	): Observable<EmailRecorrencia> {
		return this.admMsApiRecorrenciaEmailService
			.alterar(row.codigo, row.email)
			.pipe(
				map((email: EmailRecorrencia) => {
					this.notificationService.success("Email alterado");
					return email;
				})
			);
	}

	onAddEmailRecorrencia(
		row: { email: string; codigo: number },
		data: any,
		index: number
	): Observable<EmailRecorrencia> {
		return this.admMsApiRecorrenciaEmailService.incluir(row.email).pipe(
			map((email: EmailRecorrencia) => {
				this.notificationService.success("Email de recorrência incluído");
				return email;
			})
		);
	}

	onDeleteEmailRecorrencia(
		row: { email: string; codigo: number },
		data: EmailRecorrencia,
		index: number
	): Observable<boolean> {
		return this.admMsApiRecorrenciaEmailService
			.excluir(data[index].codigo)
			.pipe(
				map(() => {
					this.notificationService.success("Email de recorrência excluído");
					return null;
				})
			);
	}

	getInputsQuestionarioSessao(): SubGrupoInputs {
		return {
			subgrupo: ConfigModuloSubGroup.ADM_QUESTIONARIOS_SESSAO,
			inputs: [
				{
					name: "questionarioprimeiracompra",
					title: "Quest. de Primeira de Compra",
					description:
						'O "Quest. de Primeira de Compra" se refere ao questionário que o cliente responde durante sua primeira compra de produtos ou serviços na academia. Esse questionário é apresentado durante o processo de compra para obter informações relevantes do cliente.',
					type: ConfigItemType.SELECT,
					options: this.getOptionsQuestionario(),
				},
				{
					name: "nrdiasvigentequestionarioprimeiracompra",
					title: "Número de Dias Vigentes Para Questionário Primeira Compra",
					description:
						'O "Número de Dias Vigentes Para Questionário de Primeira Compra" se refere ao período de tempo em que as respostas ao questionário de primeira compra permanecem válidas e relevantes para o cliente. Se o cliente retornar para fazer uma nova compra dentro desse período de dias vigentes, o sistema não irá solicitar um novo questionário de primeira compra.',
					min: 0,
					validators: [Validators.required, Validators.min(0)],
					type: ConfigItemType.NUMBER,
				},
				{
					name: "questionarioretornocompra",
					title: "Quest. de Retorno de Compra",
					description:
						'O "Questionário de Retorno de Compra" é um conjunto de perguntas que o cliente responde quando retorna para fazer uma nova compra ou transação após um período específico. Essas perguntas podem estar relacionadas à experiência anterior de compra, preferências, feedback ou outras informações relevantes para entender o comportamento do cliente.',
					type: ConfigItemType.SELECT,
					options: this.getOptionsQuestionario(),
				},
				{
					name: "nrdiasvigentequestionarioretornocompra",
					title: "Número de Dias Vigentes Para Questionário Retorno de Compra",
					description:
						'O "Número de Dias Vigentes Para Questionário Retorno de Compra" é um parâmetro que define a quantidade de dias após a compra que o questionário de retorno estará disponível para o cliente responder. Esse questionário é apresentado aos clientes que já realizaram uma compra e voltam à empresa após um certo período.',
					min: 0,
					validators: [Validators.required, Validators.min(0)],
					type: ConfigItemType.NUMBER,
				},
			],
		};
	}

	getOptionsQuestionario() {
		return this.questionarios.map((questionario) => {
			return {
				id: questionario.codigo,
				label: questionario.nomeinterno,
			};
		});
	}

	getInputsConvites(): SubGrupoInputs {
		return {
			subgrupo: ConfigModuloSubGroup.ADM_CONVITES,
			inputs: [
				{
					name: "tituloConvite",
					title: "Titulo Convite",
					maxlength: 100,
					type: ConfigItemType.TEXT,
				},
				{
					name: "descricaoConvite",
					title: "Descrição do Convite",
					description:
						"Se quiser personalizar com parágrafos, emojis ou alguma estrutura diferente, é so copiar e colar o contéudo já formatado neste campo. O estilo será mantido.",
					size: "large",
					maxlength: 255,
					type: ConfigItemType.TEXT_AREA,
					rows: 3,
				},
				{
					name: "tempoReabilitacaoExAluno",
					title: "Tempo para reabilitação para ex-alunos",
					min: 1,
					validators: [Validators.min(1)],
					type: ConfigItemType.NUMBER,
				},
			],
		};
	}

	getInputsQuestionarios(): SubGrupoInputs {
		return {
			subgrupo: ConfigModuloSubGroup.ADM_QUESTIONARIOS,
			inputs: [
				{
					name: "questionarioprimeiravisita",
					title: "Quest. de Primeira Visita",
					description:
						"Refere a um questionário específico que é selecionado para ser apresentado quando um cliente está realizando o cadastro durante sua primeira visita à academia ou estabelecimento. Esse questionário tem o propósito de coletar informações relevantes sobre o cliente desde o início, especialmente em seu primeiro contato com o local.",
					type: ConfigItemType.SELECT,
					options: this.getOptionsQuestionario(),
				},
				{
					name: "nrdiasvigentequestionariovisita",
					title: "Número de Dias Vigentes Para Questionário Primeira Visita",
					description:
						'O "Número de Dias Vigentes Para Questionário de Primeira Visita" é um parâmetro configurável que determina por quantos dias o questionário preenchido durante o cadastro do visitante permanecerá válido. Se o visitante retornar após esse período e decidir contratar algum plano, o sistema solicitará que ele preencha um novo questionário de primeira visita.',
					min: 0,
					validators: [Validators.required, Validators.min(0)],
					type: ConfigItemType.NUMBER,
				},
				{
					name: "questionarioretorno",
					title: "Quest. de Retorno",
					description:
						'O "Questionário de Retorno" é um conjunto de perguntas preparadas pela empresa para serem respondidas pelos clientes quando retornam à academia após um determinado período e desejam contratar um plano.',
					type: ConfigItemType.SELECT,
					options: this.getOptionsQuestionario(),
				},
				{
					name: "nrdiasvigentequestionarioretorno",
					title: "Número de Dias Vigentes Para Questionário Primeira Visita",
					description:
						'O "Número de Dias Vigentes Para Questionário de Retorno" corresponde ao período em que o questionário de retorno estará ativo para um cliente que já foi visitante.Quando o cliente retorna à academia após esse período estabelecido (o número de dias vigentes), e está prestes a contratar um plano, o sistema solicitará que ele responda ao "Questionário de Retorno".',
					min: 0,
					validators: [Validators.required, Validators.min(0)],
					type: ConfigItemType.NUMBER,
				},
				{
					name: "questionariorematricula",
					title: "Quest. de Rematrícula",
					description:
						'O "Questionário de Rematrícula" corresponde a um conjunto de perguntas que o cliente deve responder quando está realizando sua rematrícula na academia. Esse questionário é uma ferramenta importante para atualizar as informações do cliente, entender suas necessidades e condições atuais de saúde antes de renovar o contrato.',
					type: ConfigItemType.SELECT,
					options: this.getOptionsQuestionario(),
				},
				{
					name: "nrdiasvigentequestionariorematricula",
					title: "Número de Dias Vigentes Para Questionário Rematrícula",
					description:
						'"Número de Dias Vigentes Para Questionário de Rematrícula" é o período durante o qual o questionário de rematrícula estará disponível para o cliente antes da necessidade de renovação de contrato. Se o cliente decidir renovar seu contrato dentro desse período, ele será solicitado a preencher o questionário de rematrícula.',
					min: 0,
					validators: [Validators.required, Validators.min(0)],
					type: ConfigItemType.NUMBER,
				},
			],
		};
	}

	getInputsBaisco(): SubGrupoInputs {
		return {
			subgrupo: ConfigModuloSubGroup.ADM_BASICO,
			inputs: [
				{
					name: "mascaramatricula",
					title: "Máscara Matrícula (Digite Apenas Letra x)",
					validators: [Validators.required],
					description:
						"Refere a um numero padrão ou formato que é usado para configurar a matrícula ou identificação única de clientes no sistema.										",
					textMask: Array(10).fill(/x/),
					type: ConfigItemType.TEXT,
				},
				{
					name: "carenciarenovacao",
					title: "Carência Renovação",
					min: 1,
					max: 28,
					validators: [
						Validators.required,
						Validators.min(1),
						Validators.max(28),
					],
					errorMsg: "Esta configuração deve ser entre 1 e 28 dias",
					description:
						'A "carência de renovação" é um conceito comum em muitas instituições, especialmente em academias e organizações que trabalham com planos de assinatura ou renovação periódica, ou seja, o número de dias que o aluno possui para realizar a sua renovação após o vencimento de seu plano. Depois desse período, caso o aluno não tenha realizado a renovação, se torna inativo Vencido.Não será possível colocar mais que 28 dias na opção Carência Renovação, pois assim o aluno corre o risco de ficar fora das estatísticas do mês, perdendo assim o contato com o aluno.',
					type: ConfigItemType.NUMBER,
				},
				{
					name: "nrdiasavencer",
					title: "Número de Dias A Vencer Renovação",
					min: 1,
					max: 28,
					validators: [
						Validators.required,
						Validators.min(1),
						Validators.max(28),
					],
					errorMsg: "Esta configuração deve ser entre 1 e 28 dias",
					description:
						'O "número de dias a vencer renovação" é uma ferramenta de gestão crucial que permite às academias identificar e agir proativamente sobre os contratos que estão prestes a vencer, para um cliente se tornar ativo a vencer, ou seja, que esteja próximo da data final do seu contrato;contribuindo para a eficiência na administração dos serviços oferecidos.',
					type: ConfigItemType.NUMBER,
				},
				{
					name: "carencia",
					title: "Número Mínimo de Dias para Cada Solicitação de Férias",
					min: 0,
					description:
						"Essa configuração determina um número mínimo de dias, para que o aluno retire suas férias. Caso o aluno queira solicitar um período de férias inferior a quantidade de dias informados neste campo, o sistema não irá permitir (desde que esteja configurado dentro do cadastro do plano, a quantidade de dias permitidos para retirada de férias, antes da venda do plano).",
					validators: [Validators.required, Validators.min(0)],
					errorMsg: "O valor mínimo para esta configuração é 0",
					type: ConfigItemType.NUMBER,
				},
				{
					name: "nrdiasprorata",
					title: "Número Máx.de Dias para Gerar um Mês Pró-Rata a Mais",
					errorMsg: "Esta configuração não pode ser maior que 30",
					max: 30,
					validators: [Validators.required, Validators.max(30)],
					description:
						"Essa configuração irá determinar o número máximo de dias que será considerado para gerar um mês a mais de pró-rata.",
					type: ConfigItemType.NUMBER,
				},
				{
					name: "vencimentocolaborador",
					title: "Dia de Vencimento do Personal Trainer",
					errorMsg: "Esta configuração não pode ser maior que 30",
					max: 30,
					description:
						'A configuração do "dia de vencimento do personal trainer" ajuda a estabelecer um calendário de pagamentos para os personal trainers,como data de vencimento da taxa dos personais proporcionando clareza e consistência nos processos financeiros da academia.(Se informar uma data no perfil do colaborador, esta configuração será desconsiderada).',
					validators: [Validators.required, Validators.max(30)],
					type: ConfigItemType.NUMBER,
				},
				{
					name: "qtddiasestornoautomaticocontrato",
					title:
						"Quantidade de Dias após o lançamento para o Contrato ser Estornado Automaticamente (0 = desabilitado)",
					errorMsg: "Esta configuração não pode ser menor que 0",
					min: 0,
					description:
						"Essa é uma configuração importante que determina a automação do estorno de contratos não pagos após um determinado número de dias a partir do lançamento do contrato. Quando um aluno adquirir um plano, o sistema vai verificar se da data de lançamento do contrato até o número de dias configurado nesse campo não tiver nenhuma parcela paga, então o sistema estorna automaticamente esse plano. Se tiver uma parcela paga o contrato não é estornado.",
					validators: [Validators.required, Validators.min(0)],
					type: ConfigItemType.NUMBER,
				},
				{
					name: "qtddiaprimeiraparcelavencidaestornarcontrato",
					title:
						"Quantidade de Dias após o vencimento da primeira parcela do contrato, Estornar Automaticamente (0 = desabilitado)",
					errorMsg: "Esta configuração não pode ser menor que 0",
					min: 0,
					description:
						"Essa configuração é fundamental para automatizar o estorno de contratos não pagos após um determinado número de dias do vencimento da primeira parcela.Quando um aluno adquirir um plano, o sistema vai verificar se da data de vencimento da primeira parcela até o número de dias configurado nesse campo não tiver a primeira parcela paga, então o sistema estorna automaticamente esse plano. Se tiver a primeira parcela paga o contrato não é estornado.",
					validators: [Validators.required, Validators.min(0)],
					type: ConfigItemType.NUMBER,
				},
				{
					name: "qtddiaprimeiraparcelavencidaestornarcontratorigemvendasonline",
					title:
						"Quantidade de Dias após o vencimento da primeira parcela do contrato com origem VENDAS 2.0 (Vendas Online) para Estornar Automaticamente (0 = desabilitado)",
					errorMsg: "Esta configuração não pode ser menor que 0",
					min: 0,
					description:
						"Quando um aluno adquirir um plano pelo VENDAS 2.0 (Vendas Online), o sistema vai verificar se dá data de vencimento da primeira parcela até o número de dias configurado nesse campo e não tiver a primeira parcela paga, então o sistema estorna automaticamente esse plano. Se tiver a primeira parcela paga o contrato não será estornado automaticamente.",
					validators: [Validators.required, Validators.min(0)],
					type: ConfigItemType.NUMBER,
				},
				{
					name: "enviarsmsautomatico",
					title: "Permitir enviar SMS automático aos colaboradores",
					description:
						"Com essa permissão habilitada, o sistema enviará automaticamente mensagens de texto (SMS) aos colaboradores que estão vinculados ao cliente que está passando pela catraca. Isso é útil para notificar os colaboradores sobre a presença de um cliente na academia, permitindo uma melhor organização e coordenação das atividades.(Lembrando que é necessário ter pacote de SMS adquirido juntamente com o Comercial da Pacto).",
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "itemvendaavulsaautomatico",
					title: "Permitir que a venda avulsa inclua o Produto Automaticamente",
					description:
						'Com essa permissão habilitada, ao realizar uma venda avulsa e selecionar um produto durante o processo, o sistema irá incluir automaticamente esse produto na venda. Para confirmar a inclusão do produto, basta clicar em "Receber" ou pressionar a tecla "Ctrl+R". Isso agiliza o processo de venda avulsa, pois evita a necessidade de adicionar manualmente o produto após a seleção',
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "sesc",
					title: "Usar configuração SESC",
					description:
						"Ativar apenas para o SESC. Habilita a integração do nosso sistema com o sistema do SESC.",
					type: ConfigItemType.CHECKBOX,
					formControl: this.formControlUsarConfigSesc,
					autoControlCurrentValue: true,
					children: [
						{
							name: "chavepublicasesc",
							title: "Chave pública SESC",
							description:
								"Conteúdo da chave publica de segurança para integração do SESC",
							type: ConfigItemType.TEXT,
							formControl: this.formControlChavePublicaSesc,
							autoControlCurrentValue: true,
						},
						{
							name: "chaveprivadasesc",
							title: "Chave privada SESC",
							description:
								"Conteúdo da chave privada de segurança para integração do SESC",
							type: ConfigItemType.TEXT,
							formControl: this.formControlChavePrivadaSesc,
							autoControlCurrentValue: true,
						},
					],
				},
				{
					name: "sesice",
					title: "Usar configuração SESI - CE",
					description:
						"Ative esta configuração apenas se a academia pertencer à rede SESI – CE. Ao habilitar esta configuração, campos adicionais serão exibidos no cadastro de dados pessoais do cliente, incluindo informações como Necessidades Especiais, Data de validade do cadastro, Empresa e Status da matrícula. Além disso, o campo “XNUMPRO” será adicionado ao contrato do aluno.",
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "apiSescGo",
					title: "Usar API de integração SESC - GO",
					description:
						"Ativar apenas para o SESC. Ao habilitar essa configuração, será possível importar os dados do cliente do sistema SCA para a Pacto.",
					type: ConfigItemType.CHECKBOX,
					formControl: this.formControlUsarApiSescGO,
					autoControlCurrentValue: true,
					children: [
						{
							name: "usuarioApiSescGo",
							title: "Autenticação API Sesc-GO - Usuário",
							description: "Usuário do sistema SCA.",
							type: ConfigItemType.TEXT,
							formControl: this.formControlUsuarioApiSescGo,
							autoControlCurrentValue: true,
						},
						{
							name: "senhaApiSescGo",
							title: "Autenticação API Sesc-GO - Senha",
							description: "Senha do usuário do sistema SCA.",
							type: ConfigItemType.TEXT,
							formControl: this.formControlSenhaApiSescGo,
							autoControlCurrentValue: true,
						},
					],
				},
				{
					name: "cancelarcontratonaunidadeorigemaotransferiraluno",
					title:
						"Migrar parcelas e contratos de planos do tipo recorrente ao transferir aluno de unidade",
					description:
						"Ao transferir um aluno entre empresas, o contrato será cancelado na empresa de origem" +
						"e um novo contrato com a mesma vigência final será gerado na empresa de destino. " +
						"Atenção: Essa operação se aplica somente a contratos de planos recorrentes.",
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "utilizartipoplano",
					title: "Utilizar recurso para tipo de planos",
					description:
						'Essa configuração, quando habilitada, permite a utilização do recurso de "Tipo de Plano" ao cadastrar um plano. O campo "Tipo" se torna obrigatório e é necessário selecionar um dos tipos previamente cadastrados no recurso "Tipo de Plano". Esses tipos de plano contêm parametrizações relacionadas a configurações fiscais e financeiras que são utilizadas na geração das parcelas do contrato, especialmente quando há integração com o sistema Protheus. Portanto, essa configuração é relevante se a sua operação envolve uma integração com o sistema Protheus.',
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "exibirmodalusuariosinativos",
					title: "Verificar usuários inativos ao logar",
					description:
						"Essa configuração, quando habilitada, faz com que, ao fazer login no sistema, seja exibida uma tela com os usuários que estão marcados como inativos. Isso pode ser útil para que os administradores ou gestores possam visualizar e gerenciar facilmente os usuários que estão inativos no sistema. Permite uma rápida identificação dos usuários que não estão mais ativos e podem exigir alguma ação, como reativação ou exclusão.",
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "exibirModalPlanosInativos",
					title: "Verificar planos inativos ao logar",
					description:
						"Ao habilitar esta configuração, será exibido um pop-up informando quais planos ficarão inativos no mês seguinte, permitindo que o usuário tome as ações necessárias.",
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "lancamentocontratosiguais",
					title:
						"Permitir o lançamento de contratos com planos iguais desconsiderando os 30 minutos",
					description:
						"Essa configuração, quando habilitada, permite que o usuário faça o lançamento de um contrato com o mesmo plano para um aluno, independentemente do intervalo de 30 minutos que normalmente é exigido para evitar duplicações. Em outras palavras, mesmo que não tenha passado o intervalo de 30 minutos desde o último lançamento do mesmo plano para o mesmo aluno, o sistema permitirá o lançamento sem restrições. Isso pode ser útil em situações específicas em que deseja-se flexibilizar a regra de tempo entre lançamentos do mesmo plano para um aluno.",
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "propagaraassinaturadigital",
					title: "Propagar assinatura digital na renovação de contrato",
					description:
						"Essa configuração, quando habilitada, permite que a assinatura digital presente em um contrato atual seja automaticamente replicada nas renovações subsequentes desse contrato para o mesmo aluno. Isso facilita o processo de renovação, pois a assinatura digital não precisará ser repetidamente coletada a cada renovação. A assinatura já existente será propagada para garantir a continuidade da validade e autenticidade do contrato renovado.",
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "mantercontratoassinadonarenovacaocontrato",
					title: "Manter contrato assinado na renovação automática",
					description:
						"Com essa configuração, o contrato assinado na matrícula ou rematrícula será usado para as renovações automáticas.",
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "termoresponsabilidade",
					title: "Termo de responsabilidade visitante",
					description:
						"Essa configuração, quando habilitada, permite que a assinatura digital presente em um contrato atual seja automaticamente replicada nas renovações subsequentes desse contrato para o mesmo aluno. Isso facilita o processo de renovação, pois a assinatura digital não precisará ser repetidamente coletada a cada renovação. A assinatura já existente será propagada para garantir a continuidade da validade e autenticidade do contrato renovado.",
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "termoresponsabilidadeexaluno",
					title: "Termo de responsabilidade Ex-aluno e Visitante",
					description:
						"Ao habilitar esta configuração, é possível configurar um termo para que os clientes visitantes e ex-alunos assinem digitalmente concordando com os limites inerentes ao uso de um produto ou serviço fornecido pela empresa, bem como a maneira de utilização.",
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "permitetrocaempresamultichave",
					title: "Habilitar troca de empresa multi chave",
					description:
						"Essa configuração permite que o usuário troque de empresa no sistema em bancos que utilizam múltiplas chaves. Isso é especialmente relevante em ambientes onde há necessidade de trabalhar com várias empresas para acesso. Ao habilitar essa opção, o usuário poderá alternar entre diferentes chaves de autenticação para acessar informações ou operar em nome de diferentes empresas.",
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "permitelancarferiasplanorecorrente",
					title: "Permitir lançar férias para plano recorrente",
					description:
						"Essa configuração permite que os planos do tipo recorrência terão a possibilidade de configurar período de férias.",
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "usaplanorecorrentecompartilhado",
					title: "Permitir compartilhamento de plano recorrente",
					description:
						"Essa configuração permite que o usuário compartilhe um plano recorrente com outros alunos, caso a opção “Convites” esteja configurado no plano. Por exemplo, se o plano do aluno permitir compartilhamento com outros usuários, o usuário poderá conceder acesso a esse plano a outros alunos, possivelmente dividindo custos ou benefícios associados ao plano. Isso pode ser útil em casos onde um plano específico pode ser utilizado por mais de um aluno, com a permissão e configurações apropriadas.",
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "permiteimpressaocontratomutavel",
					title: "Permite impressão de contrato de forma mutável",
					description:
						"Essa configuração, quando habilitada, permite que as alterações feitas nos contratos, mesmo após terem sido lançadas para o aluno, sejam contabilizadas ao realizar a impressão do contrato. Em outras palavras, se houver modificações no contrato após o lançamento para o aluno, essas alterações serão consideradas e impressas no contrato quando for solicitada uma impressão. Isso proporciona uma documentação precisa e atualizada, refletindo todas as modificações feitas no contrato, mesmo após sua emissão inicial.(Caso esteja desmarcada irá permanecer as alteração das cláusulas contratuais).",
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "permiteestornarcontrato30minaposlancamento",
					title:
						"Permite estornar contrato até 30 minutos após o lançamento, mesmo sem a permissão '4.13 - Estorno do Contrato' habilitada",
					description:
						"Esta configuração permite que o usuário responsável pelo lançamento tenha até 30 minutos após o lançamento do contrato para realizar o estorno do mesmo. " +
						"Independente do usuário possuir ou não a permissão '4.13 - Estorno do Contrato'.",
					type: ConfigItemType.CHECKBOX,
				},
			],
		};
	}

	getInputsAcesso(): SubGrupoInputs {
		return {
			subgrupo: ConfigModuloSubGroup.ADM_ACESSO,
			inputs: [
				{
					name: "toleranciapagamento",
					title: "Dias de tolerância para pagamento",
					min: 0,
					validators: [Validators.required, Validators.min(0)],
					description:
						"Essa configuração define a quantidade de dias de tolerância que o cliente poderá ter acesso a academia com parcela vencida. Essa configuração é válida somente para parcelas em aberto de contratos.",
					type: ConfigItemType.NUMBER,
				},
				{
					name: "toleranciadiascontratovencido",
					title: "Tolerância Contrato Vencido",
					min: 0,
					validators: [Validators.required, Validators.min(0)],
					description:
						"Essa configuração determina a quantidade de dias de tolerância após o vencimento do contrato, durante os quais o cliente ainda terá acesso à academia. Após o vencimento do contrato, o cliente poderá continuar frequentando a academia durante esse período de tolerância. É uma medida para oferecer um tempo adicional ao cliente para renovar ou regularizar o contrato antes que o acesso seja restrito.",
					type: ConfigItemType.NUMBER,
				},
				{
					name: "bloquearacessoseparcelaaberta",
					title: "Bloquear acesso se houver alguma parcela em aberto",
					description:
						"Ao marcar esta configuração, o sistema bloqueará o acesso do aluno caso haja alguma parcela em aberto, excluindo as parcelas de recorrência. O acesso será liberado somente quando todas as parcelas, exceto as de recorrência, estiverem quitadas. É uma maneira de garantir que o aluno esteja em dia com seus pagamentos para manter o acesso à academia.",
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "forcarcodigoalternativoacesso",
					title: "Forçar Validação por Código Alternativo",
					description:
						"Essa opção permite a validação de acesso Quando há a necessidade de utilizar um código de carteirinha que não é padronizado pelo sistema (Diferente de 8 dígitos), essa configuração possibilita que o usuário utilize o Código de Acesso Alternativo do Cliente e/ou Colaborador, para a Validação de Acesso pela Catraca.Observação: Quando utiliza-se essa configuração, o Código de Acesso tradicional é desconsiderado, tanto do Cliente, quanto do Colaborador.",
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "marcarpresencapeloacesso",
					title: "Marcar Presença Pelo Acesso",
					description:
						"Quando essa configuração estiver marcado esta configuração, assim que o aluno acessar a catraca, será registrada a presença do mesmo.",
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "controleacessomultiplasempresasporplano",
					title: "Controle de acesso em várias empresas com definição no plano",
					description:
						'Essa opção viabiliza o controle de acesso em várias empresas dentro do mesmo banco de dados (multiempresa). Ao ativá-la, é possível definir planos específicos que concedem acesso a determinadas unidades. No cadastro do plano, será apresentada uma nova aba "Empresa", permitindo a configuração das unidades a que o aluno terá acesso com esse plano.',
					type: ConfigItemType.CHECKBOX,
				},
				{
					title: "Configuração de Acesso para Cliente Em Risco",
					type: ConfigItemType.GROUP,
					children: [
						{
							name: "qtdfaltapeso1",
							title:
								"Quantidade Máxima de DIAS que o cliente pode Faltar para obter Peso 1 na tabela de risco",
							description:
								"Esse campo permite configurar a quantidade máxima de dias que um cliente pode faltar para obter o peso 1 na tabela de risco. A tabela de risco é um mecanismo que avalia a frequência do cliente, atribuindo pesos com base na regularidade das presenças nas atividades da academia.",
							min: 0,
							validators: [Validators.required, Validators.min(0)],
							type: ConfigItemType.NUMBER,
						},
						{
							title:
								"Quantidade Mínima e Máxima de DIAS que o cliente pode Faltar para obter Peso 2 na tabela de risco",
							description:
								"Este campo permite configurar a quantidade mínima e máxima de dias que um cliente pode faltar para obter o peso 2 na tabela de risco. A tabela de risco avalia a frequência do cliente, atribuindo pesos com base na regularidade das presenças nas atividades da academia. O peso 2 pode indicar uma frequência razoável, mas com alguma falta.",
							type: ConfigItemType.RANGE,
							children: [
								{
									name: "qtdfaltainiciopeso2",
									title: "Minimo",
									min: 0,
									validators: [Validators.required, Validators.min(0)],
									type: ConfigItemType.NUMBER,
								},
								{
									name: "qtdfaltaterminopeso2",
									title: "Máximo",
									min: 0,
									validators: [Validators.required, Validators.min(0)],
									type: ConfigItemType.NUMBER,
								},
							],
						},
						{
							name: "qtdfaltapeso3",
							title:
								"Quantidade Mínima de DIAS que o cliente pode Faltar para obter Peso 3 na tabela de risco",
							min: 0,
							validators: [Validators.required, Validators.min(0)],
							description:
								"Este campo permite configurar a quantidade mínima de dias que um cliente pode faltar para obter o peso 3 na tabela de risco. Na tabela de risco, a frequência dos clientes é avaliada, e pesos são atribuídos com base na regularidade das presenças nas atividades da academia. O peso 3 pode indicar uma frequência moderada, com alguma margem para faltas.",
							type: ConfigItemType.NUMBER,
						},
						{
							title: "Período a desconsiderar",
							description:
								"Neste campo informe um período para que seja desconsiderado a quantidade mínima de dias para faltar.",
							type: ConfigItemType.RANGE,
							children: [
								{
									name: "datainiciodesconsideraracessorisco",
									title: "De",
									description:
										"Data de inicio para desconsiderar o acesso de risco do cliente que são definidos nas configurações qtdfaltapeso1, qtdfaltainiciopeso2, qtdfaltaterminopeso2, qtdfaltapeso3",
									type: ConfigItemType.DATE,
								},
								{
									name: "datafimdesconsideraracessorisco",
									title: "Até",
									description:
										"Data de fim para desconsiderar o acesso de risco do cliente que são definidos nas configurações qtdfaltapeso1, qtdfaltainiciopeso2, qtdfaltaterminopeso2, qtdfaltapeso3",
									type: ConfigItemType.DATE,
								},
							],
						},
					],
				},
				{
					title: "Configuração de intervalo de acessos GymPass",
					type: ConfigItemType.GROUP,
					children: [
						{
							title: "Faixa 1: quantidade de acessos entre",
							description:
								"Determine neste campo uma quantidade mínima e máxima de acessos que um cliente Gympass deve possui, para ser considerado como Faixa 1 no BI Gympass.",
							type: ConfigItemType.RANGE,
							children: [
								{
									name: "qtdacessogympassiniciofaixa1",
									title: "Mínimo",
									min: 0,
									validators: [Validators.required, Validators.min(0)],
									type: ConfigItemType.NUMBER,
								},
								{
									name: "qtdacessogympassfinalfaixa1",
									title: "Máximo",
									min: 0,
									validators: [Validators.required, Validators.min(0)],
									type: ConfigItemType.NUMBER,
								},
							],
						},
						{
							title: "Faixa 2: quantidade de acessos entre",
							description:
								"Determine neste campo uma quantidade mínima e máxima de acessos que um cliente Gympass deve possui, para ser considerado como Faixa 2 no BI Gympass.",
							type: ConfigItemType.RANGE,
							children: [
								{
									name: "qtdacessogympassiniciofaixa2",
									title: "Minimo",
									min: 0,
									validators: [Validators.required, Validators.min(0)],
									type: ConfigItemType.NUMBER,
								},
								{
									name: "qtdacessogympassfinalfaixa2",
									title: "Máximo",
									min: 0,
									validators: [Validators.required, Validators.min(0)],
									type: ConfigItemType.NUMBER,
								},
							],
						},
						{
							title: "Faixa 3: quantidade de acessos entre",
							description:
								"Determine neste campo uma quantidade mínima e máxima de acessos que um cliente Gympass deve possui, para ser considerado como Faixa 3 no BI Gympass.",
							type: ConfigItemType.RANGE,
							children: [
								{
									name: "qtdacessogympassiniciofaixa3",
									title: "Mínimo",
									min: 0,
									validators: [Validators.required, Validators.min(0)],
									type: ConfigItemType.NUMBER,
								},
								{
									name: "qtdacessogympassfinalfaixa3",
									title: "Máximo",
									min: 0,
									validators: [Validators.required, Validators.min(0)],
									type: ConfigItemType.NUMBER,
								},
							],
						},
						{
							title: "Faixa 4: quantidade de acessos entre",
							description:
								"Determine neste campo uma quantidade mínima e máxima de acessos que um cliente WellHub deve possui, para ser considerado como Faixa 4 no BI WellHub.",
							type: ConfigItemType.RANGE,
							children: [
								{
									name: "qtdacessogympassiniciofaixa4",
									title: "Mínimo",
									min: 0,
									validators: [Validators.required, Validators.min(0)],
									type: ConfigItemType.NUMBER,
								},
								{
									name: "qtdacessogympassfinalfaixa4",
									title: "Máximo",
									min: 0,
									validators: [Validators.required, Validators.min(0)],
									type: ConfigItemType.NUMBER,
								},
							],
						},
					],
				},
			],
		};
	}

	getVisitantesInputNames(): string[] {
		if (!this.currentValuesCamposVisitante) {
			this.currentValuesCamposVisitante = new Array<ConfiguracaoCliente>();
		}
		return this.currentValuesCamposVisitante.map((campo) => {
			return campo.nome;
		});
	}

	getInputsVisitante(): SubGrupoInputs {
		return {
			subgrupo: ConfigModuloSubGroup.ADM_CAMPOS_VISITANTE,
			inputs: this.getVisitantesInputNames().map((name) => {
				return {
					name,
					descriptionMode:
						'O usuário poderá marcar ou desmarcar as opções (apresentar, necessário e obrigatório) de acordo com sua gestão: Apresentar: Ao marcar um campo como "Apresentar", ele não é validado no cadastro e não é exibido na mensagem para o colaborador sobre campos não preenchidos. Necessário: Quando um campo é marcado como "Necessário", ele é automaticamente marcado como "Apresentar" e é exibido na mensagem para o colaborador indicando os campos pendentes se não for preenchido no cadastro.Obrigatório: Ao marcar um campo como "Obrigatório", ele é automaticamente marcado como "Apresentar" e é validado na tela de cadastro do colaborador, exigindo que seja preenchido.',
					fieldLogName: `${name} no cadastro do visitante`,
					title: name,
					type: ConfigItemType.RADIO,
					formControl: this.createFormControlVisitante(name),
					options: this.getSelectOptionsCampoCliente(),
				};
			}),
		};
	}

	getInputsCliente(): SubGrupoInputs {
		return {
			subgrupo: ConfigModuloSubGroup.ADM_CAMPOS_CLIENTE,
			inputs: this.getClientesInputNames().map((name) => {
				return {
					name,
					descriptionMode:
						'O usuário poderá marcar ou desmarcar as opções (apresentar, necessário e obrigatório) de acordo com sua gestão: Apresentar: Ao marcar um campo como "Apresentar", ele não é validado no cadastro e não é exibido na mensagem para o colaborador sobre campos não preenchidos. Necessário: Quando um campo é marcado como "Necessário", ele é automaticamente marcado como "Apresentar" e é exibido na mensagem para o colaborador indicando os campos pendentes se não for preenchido no cadastro.Obrigatório: Ao marcar um campo como "Obrigatório", ele é automaticamente marcado como "Apresentar" e é validado na tela de cadastro do colaborador, exigindo que seja preenchido.',
					title: name,
					fieldLogName: `${name} no cadastro do cliente`,
					type: ConfigItemType.RADIO,
					formControl: this.createFormControlCliente(name),
					options: this.getSelectOptionsCampoCliente(),
				};
			}),
		};
	}

	getInputsOutas(): SubGrupoInputs {
		return {
			subgrupo: ConfigModuloSubGroup.ADM_OUTRAS,
			inputs: [
				{
					name: "cpfvalidar",
					title: "Validar CPF",
					description:
						"A validação de CPF, quando habilitada, assegura que o usuário está inserindo um número de CPF válido e não padrões como 111.111.111-11 ou 999.999.999-99, entre outros inválidos. Isso é especialmente importante para academias que utilizam o módulo de Nota Fiscal de Serviços Eletrônica (NFSe) ou Nota Fiscal do Consumidor Eletrônica (NFCe), evitando problemas de autorização de notas fiscais devido a CPFs inválidos. Ao marcar essa opção, o sistema verifica e valida os CPFs no momento do cadastro do cliente, garantindo que não haja duplicidade ou CPFs inválidos na configuração do sistema.",
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "validarcpfduplicado",
					title: "Validar se já existe o CPF em banco",
					description:
						"Com a permissão de validar se já existe o CPF em banco habilitada, o sistema verifica se o CPF informado no momento do cadastro já está registrado no banco de dados. Se o CPF já existe, o sistema oferece a opção de vincular o novo aluno como dependente do aluno que possui o CPF cadastrado. Isso promove a integração e organização dos registros, permitindo a associação de dependentes de forma eficiente no sistema.",
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "nomedatanascvalidar",
					title: "Validar Nome e Data de Nascimento",
					description:
						"Com a permissão de validar nome e data de nascimento habilitada, o sistema verifica no momento da inclusão de um visitante se já existe outro aluno com o mesmo nome e data de nascimento no banco de dados. Essa validação auxilia na prevenção de duplicatas ou registros incorretos, garantindo a integridade e organização dos dados no sistema.",
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "usarnomeresponsavelnota",
					title:
						"Emitir nota fiscal no nome e CPF do responsável caso aluno seja menor de 18 anos",
					description:
						"Ao marcar essa configuração, o sistema exigirá a inclusão de um responsável para alunos menores de 18 anos no momento da emissão de nota fiscal. Isso garante que a nota fiscal seja emitida no nome e CPF do responsável legal quando o aluno é menor de idade, assegurando o cumprimento das normativas e requisitos legais relacionados à emissão de documentos fiscais para essa faixa etária.",
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "validarcpfresponsaveis",
					title:
						"Não exigir CPF do aluno menor de 18 anos se houver CPF dos responsáveis cadastrado",
					description:
						"Ao habilitar essa configuração, o sistema permite que a inclusão do CPF do aluno menor de 18 anos seja opcional, desde que os dados do responsável já estejam cadastrados e associados ao aluno. Isso é útil para garantir a flexibilidade no preenchimento de informações, principalmente em situações em que o responsável é o principal contato é detentor das informações fiscais, e o CPF do aluno menor pode não ser necessário para determinadas operações.",
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "urlgoogleagenda",
					title: "URL Google Agenda",
					description:
						"Neste campo deve ser informado a URL (endereço) da sua agenda virtual.",
					type: ConfigItemType.TEXT_AREA,
				},
				{
					name: "numerocielo",
					title: "Número de telefone da Cielo",
					description:
						"Neste campo deve ser informado o numero de contato da operadora Cielo.",
					type: ConfigItemType.TEXT,
				},
				{
					name: "nomenclaturavendacredito",
					title: "Nomenclatura utilizada para planos Venda Crédito de Treino",
					description:
						'"Nomenclatura utilizada para planos Venda Crédito de Treino" refere-se à forma como os planos de Venda de Créditos de Treino são nomeados e apresentados no sistema. Essa configuração permite definir uma nomenclatura específica para esses planos, facilitando a identificação e diferenciação deles dentro do contexto de vendas de créditos de treino. Por exemplo, pode-se utilizar termos como "Créditos de Treino" ou "Crédito Hora/Aula"para apresentar nos planos de venda.',
					type: ConfigItemType.SELECT,
					options: [
						{
							id: "CREDITO_TREINO",
							label: "Crédito Treino",
						},
						{
							id: "CREDITO_HORA_AULA",
							label: "Crédito Hora/Aula",
						},
					],
				},
				{
					name: "usardigitalcomoassinatura",
					title: "Usar digital como assinatura de recibo",
					description:
						'A opção "Usar digital como assinatura de recibo" permite a utilização da assinatura digital biométrica do aluno no recibo de cancelamento. Essa assinatura digital é representada por um código alfanumérico único e extenso, que é derivado do template da biometria do aluno. Ao habilitar essa configuração, o sistema incluirá essa assinatura digital no recibo de cancelamento, proporcionando uma forma adicional de autenticação e validação do cancelamento realizado pelo aluno.',
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "apresentarmarketplace",
					title: "Apresentar Pacto Store+",
					description:
						'Ao habilitar a opção "Apresentar Pacto Store+", o usuário terá acesso ao módulo de compras chamado "Pacto Store+". Esse módulo pode estar relacionado a funcionalidades ou recursos de compras, vendas ou gestão de produtos/serviços dentro do sistema, proporcionando uma interface para a realização de transações relacionadas a essas atividades.',
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "********************************",
					title: "Permite replicar plano entre rede de empresas",
					description:
						'Ao habilitar a opção "Permite replicar plano entre rede de empresas", o sistema permitirá que os planos cadastrados na matriz ou na franqueadora sejam replicados e disponibilizados para todas as demais unidades pertencentes à rede de empresas. Isso facilita a padronização e a gestão centralizada dos planos oferecidos, garantindo que as mesmas opções estejam disponíveis em todas as unidades da rede.',
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "permitirreplicarmodelocontratoredeempresa",
					title: "Permite replicar modelo contrato entre rede de empresas",
					description:
						'Ao habilitar a opção "Permite replicar modelo contrato entre rede de empresas", o sistema permitirá que os modelos de contrato cadastrados na matriz ou na franqueadora sejam replicados e disponibilizados para todas as demais unidades pertencentes à rede de empresas. Isso facilita a padronização e a gestão centralizada dos modelos de contrato utilizados, garantindo que os mesmos modelos estejam disponíveis em todas as unidades da rede.',
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "permitirreplicarferiadoredeempresa",
					title: "Permite replicar feriado entre rede de empresas",
					description:
						'Ao habilitar a opção "Permite replicar feriado entre rede de empresas", o sistema permitirá que os feriados cadastrados na matriz ou na franqueadora sejam replicados e disponibilizados para todas as demais unidades pertencentes à rede de empresas. Isso facilita a padronização dos feriados utilizados em todas as unidades, garantindo que os mesmos feriados estejam disponíveis e sejam aplicados consistentemente em toda a rede.',
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "permitirreplicarperfilacessoredeempresa",
					title: "Permite replicar perfil acesso entre rede de empresas",
					description:
						'Ao habilitar a opção "Permite replicar perfil de acesso entre rede de empresas", o sistema permitirá que os perfis de acesso cadastrados, juntamente com suas permissões e configurações, na matriz ou na franqueadora sejam replicados e disponibilizados para todas as demais unidades pertencentes à rede de empresas. Isso facilita a padronização dos perfis de acesso e permissões utilizados em todas as unidades, garantindo uma consistência nas configurações de acesso em toda a rede.',
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "permitirreplicarusuarioredeempresa",
					title: "Permite replicar usuario entre rede de empresas",
					description:
						'Ao habilitar a opção "Permite replicar usuário entre rede de empresas", o sistema permitirá que os usuários cadastrados na matriz ou na franqueadora sejam replicados e disponibilizados para todas as demais unidades pertencentes à rede de empresas. Isso facilita a gestão dos usuários em todas as unidades, garantindo uma padronização e consistência nos perfis de acesso utilizados em toda a rede.',
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "forcarutilizacaoplanoantigo",
					title: "Forçar utilização da tela de plano antiga",
					description:
						'Ao habilitar a opção "Forçar utilização da tela de plano antiga", o sistema direcionará automaticamente os usuários para a tela antiga de cadastro de planos quando acessarem essa funcionalidade. Isso pode ser útil quando a equipe está mais familiarizada e acostumada com a interface antiga ou quando há necessidade de manter um fluxo de trabalho específico que depende da tela antiga de cadastro de planos.Ao habilitar a opção "Forçar utilização da tela de plano antiga", o sistema direcionará automaticamente os usuários para a tela antiga de cadastro de planos quando acessarem essa funcionalidade. Isso pode ser útil quando a equipe está mais familiarizada e acostumada com a interface antiga ou quando há necessidade de manter um fluxo de trabalho específico que depende da tela antiga de cadastro de planos.',
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "loginatravesazuread",
					title: "Ativar login através integração AZURE AD",
					description:
						"Ativar apenas para o SESC. Ao habilitar essa configuração, será possível importar os dados do cliente do sistema SCA para a Pacto.",
					type: ConfigItemType.CHECKBOX,
					formControl: this.formControlAtivarLoginAzureAD,
					autoControlCurrentValue: true,
					children: [
						{
							name: "azureadtenatid",
							title: "Tenant ID",
							type: ConfigItemType.TEXT,
							formControl: this.formControlAzureADTenantId,
							autoControlCurrentValue: true,
						},
						{
							name: "azureadclientid",
							title: "Client ID",
							type: ConfigItemType.TEXT,
							formControl: this.formControlAzureADClientID,
							autoControlCurrentValue: true,
						},
					],
				},
			],
		};
	}

	getInputsColaborador(): SubGrupoInputs {
		if (!this.currentValuesCamposColaborador) {
			this.currentValuesCamposColaborador =
				new Array<ConfiguracaoColaborador>();
		}
		return {
			subgrupo: ConfigModuloSubGroup.ADM_CAMPOS_COLABORADOR,
			inputs: this.currentValuesCamposColaborador.map((campo) => {
				return {
					name: campo.nomecampo,
					fieldLogName: `${campo.labelcampo} no cadastro do colaborador`,
					title: campo.labelcampo,
					descriptionMode:
						'O usuário poderá marcar ou desmarcar as opções (apresentar, necessário e obrigatório) de acordo com sua gestão: Apresentar: Ao marcar um campo como "Apresentar", ele não é validado no cadastro e não é exibido na mensagem para o colaborador sobre campos não preenchidos. Necessário: Quando um campo é marcado como "Necessário", ele é automaticamente marcado como "Apresentar" e é exibido na mensagem para o colaborador indicando os campos pendentes se não for preenchido no cadastro.Obrigatório: Ao marcar um campo como "Obrigatório", ele é automaticamente marcado como "Apresentar" e é validado na tela de cadastro do colaborador, exigindo que seja preenchido.',
					type: ConfigItemType.RADIO,
					formControl: this.createFormControlColaborador(campo.nomecampo),
					options: [
						{
							id: "naomostrar",
							label: "Não apresentar",
						},
						{
							id: "mostrarcampo",
							label: "Apresentar",
						},
						{
							id: "campoobrigatorio",
							label: "Obrigatório",
						},
					],
				};
			}),
		};
	}

	createFormControlCliente(name: string): any {
		return this._createFormControlCliente(
			name,
			this.currentValuesCamposCliente,
			this.formGroupCamposCliente
		);
	}

	createFormControlColaborador(name: string): any {
		return this._createFormControlColaborador(
			name,
			this.currentValuesCamposColaborador,
			this.formGroupCamposColaborador
		);
	}

	private getSelectOptionsCampoCliente(): { id: string; label: string }[] {
		return [
			{
				id: "naomostrar",
				label: "Não apresentar",
			},
			{
				id: "mostrar",
				label: "Apresentar",
			},
			{
				id: "pendente",
				label: "Necessário",
			},
			{
				id: "obrigatorio",
				label: "Obrigatório",
			},
		];
	}

	getClientesInputNames(): string[] {
		if (!this.currentValuesCamposCliente) {
			this.currentValuesCamposCliente = new Array<ConfiguracaoCliente>();
		}
		return this.currentValuesCamposCliente.map((campo) => {
			return campo.nome;
		});
	}

	private _createFormControlColaborador(
		nomecampo: string,
		currentValues: Array<ConfiguracaoColaborador>,
		formGroup: FormGroup
	): FormControl {
		const campo = currentValues.find(
			(campo) => campo.nomecampo.trim() === nomecampo.trim()
		);

		if (!campo) {
			throw new Error(
				`O valor atual da configuração ${nomecampo} não foi encontrada ao criar o seu form control`
			);
		}

		let currentValue = null;

		if (campo.mostrarcampo === true) currentValue = "mostrarcampo";
		if (campo.campoobrigatorio === true) currentValue = "campoobrigatorio";
		if (campo.mostrarcampo == false && campo.campoobrigatorio == false)
			currentValue = "naomostrar";

		const formControl = new FormControl(currentValue);
		formGroup.addControl(nomecampo, formControl);
		return formControl;
	}

	private _createFormControlCliente(
		nomecampo: string,
		currentValues: Array<ConfiguracaoCliente>,
		formGroup: FormGroup
	): FormControl {
		const campo = currentValues.find(
			(campo) => campo.nome.trim() === nomecampo.trim()
		);

		if (!campo) {
			throw new Error(
				`O valor atual da configuração ${nomecampo} não foi encontrada ao criar o seu form control`
			);
		}

		let currentValue = null;

		if (campo.obrigatorio === true) currentValue = "obrigatorio";
		if (campo.pendente === true) currentValue = "pendente";
		if (campo.mostrar === true && !campo.obrigatorio && !campo.pendente)
			currentValue = "mostrar";
		if (
			campo.mostrar == false &&
			campo.obrigatorio == false &&
			campo.pendente == false
		)
			currentValue = "naomostrar";

		const formControl = new FormControl(currentValue);
		formGroup.addControl(nomecampo, formControl);
		return formControl;
	}

	private createFormControlVisitante(nomecampo: string): FormControl {
		return this._createFormControlCliente(
			nomecampo,
			this.currentValuesCamposVisitante,
			this.formGroupCamposVisitante
		);
	}

	excluirAlunosCatrata() {
		if (
			!this.formControlExcluirColaboradores.value &&
			!this.formControlExcluirAlunos.value
		) {
			this.notificationService.error(
				"Selecione ao menos um tipo de senha para ser excluída."
			);
		} else {
			const titulo = "Remover alunos ?";
			const body = `Tem certeza que deseja excluir senhas de Acesso Catraca - 
					${this.formControlExcluirColaboradores.value ? "Colaboradores, " : ""} 
					${this.formControlExcluirAlunos.value ? "Alunos, " : ""} 
				  Esse processo pode demorar alguns minutos.`;
			const styles = `modal-title-sm modal-body-sm`;
			const appModal = this.modalService.confirmModal(
				titulo,
				body,
				"Excluir",
				"lg",
				styles
			);
			const hasColaboradores = this.formControlExcluirColaboradores.value
				? true
				: false;
			const hasAlunos = this.formControlExcluirAlunos.value ? true : false;

			appModal.result.then(() => {
				this.admConfigApiService
					.executarExclusaoCatrata(1, hasColaboradores, hasAlunos)
					.subscribe({
						next: (res) => {
							this.notificationService.success(
								"Senhas de acesso excluídas com sucesso."
							);
						},
						error: (error) => {
							this.notificationService.error(error.message);
						},
					});
			});
		}
	}

	verificarMatricula() {
		const matriculaCliente = this.formControlPesquisarMatricula.value;
		if (!matriculaCliente) {
			this.notificationService.error("Informe uma matrícula para pesquisa.");
			return;
		}

		this.admConfigApiService.verificarMatricula(matriculaCliente).subscribe({
			next: (res) => {
				this.notificationService.info(res);
			},
			error: (error) => {
				this.notificationService.error(error.message);
			},
		});
	}

	excluirCliente() {
		if (!this.formControlPesquisarMatricula.value) {
			this.notificationService.error(
				"Informe uma matricula para ser excluída."
			);
		} else {
			const clienteMatricula = this.formControlPesquisarMatricula.value;
			const titulo = "Remover alunos ?";
			const body = `Tem certeza que deseja excluir o cliente ${clienteMatricula.pessoa.nome} com a matrícula ${clienteMatricula.matricula} Esse processo pode demorar alguns minutos.`;
			const styles = `modal-title-sm modal-body-sm`;
			const appModal = this.modalService.confirmModal(
				titulo,
				body,
				"Excluir",
				"lg",
				styles
			);

			appModal.result.then(() => {
				this.admConfigApiService
					.executarExclusaoCliente(clienteMatricula.matricula)
					.subscribe({
						next: () => {
							this.notificationService.success("Cliente excluido com sucesso.");
						},
						error: (httpResponseError) => {
							const error = httpResponseError.error;
							let message = httpResponseError.message;
							if (error) {
								if (error.message) {
									message = error.message;
								}
							}
							this.notificationService.error(message);
						},
					});
			});
		}
	}
}
