import {
	ChangeDetector<PERSON><PERSON>,
	Component,
	OnInit,
	ViewChild,
	NgZone, Output, EventEmitter
} from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { MatDialog } from "@angular/material";
import { ActivatedRoute, Router } from "@angular/router";
import { AdmCoreApiSolicitacaoCompraService } from "adm-core-api";
import { SnotifyService } from "ng-snotify";
import { CatFileInputComponent, DialogService, PactoDataGridConfig, PactoModalSize } from 'ui-kit';
import { AdmRestService } from "../../adm-rest.service";
import { ModalConfirmDisapproveComponent } from "../modal-confirm-disapprove/modal-confirm-disapprove.component";
import { UploadArquivo } from '@adm/solicitacao-compra/documentos/documentos-container/classes/upload-arquivo.model';
import { TipoUploadEnum } from '@adm/solicitacao-compra/documentos/documentos-container/classes/tipo-upload.enum';
import {
	SolicitacaoCompraDocumentosTableComponent
} from '@adm/solicitacao-compra/documentos/documentos-container/documentos-table/solicitacao-compra-documentos-table.component';
import {
	ModalSolicitacaoCompraUploadArquivosComponent
} from '@adm/solicitacao-compra/documentos/documentos-container/modal-upload-arquivos/modal-solicitacao-compra-upload-arquivos.component';
import { UnderConstructionComponent } from 'pacto-layout';
import { throwError } from 'rxjs';
import { ClientDiscoveryService } from 'sdk';
import { SessionService } from "@base-core/client/session.service";

@Component({
	selector: "adm-add-approve-solicitacao-compra",
	templateUrl: "./add-approve-solicitacao-compra.component.html",
	styleUrls: ["./add-approve-solicitacao-compra.component.scss"],
})
export class AddApproveSolicitacaoCompraComponent implements OnInit {
	@ViewChild("tableData", { static: true }) tableData;
	@ViewChild("fileInputComponent", { static: false })
	private readonly fileInputComponent: CatFileInputComponent;
	@ViewChild("documentosTableComponent", { static: false })
	private readonly documentosTableComponent: SolicitacaoCompraDocumentosTableComponent;
	@Output()
	goBackClient: EventEmitter<any> = new EventEmitter<any>();

	form: FormGroup;
	table: PactoDataGridConfig;
	codigo: number;
	page = 0;
	situacao: any;
	mode: any;
	documentosAlunos: Array<any>;
	uploadArquivo: UploadArquivo;

	constructor(
		private fb: FormBuilder,
		private router: Router,
		private route: ActivatedRoute,
		private snotifyService: SnotifyService,
		private admCoreApiSolicitacaoCompraService: AdmCoreApiSolicitacaoCompraService,
		private cd: ChangeDetectorRef,
		public dialog: MatDialog,
		private dialogService: DialogService,
		private zone: NgZone,
		private readonly modalService: DialogService,
		private clientDiscoveryService: ClientDiscoveryService,
		private sessionService: SessionService,
		private admRest: AdmRestService
	) {
		this.route.queryParams.subscribe((params) => {
			this.mode = params["mode"];
			this.codigo = params["codigo"];
		});
		this.buildForm();
	}

	ngOnInit() {
		if (this.codigo) {
			this.getByIdSolicitacaoCompra();
		}
		this.prepararUpload();
	}

	private prepararUpload() {
		this.form.get('file').valueChanges.subscribe((file) => {
			if (file) {
				this.uploadArquivo = new UploadArquivo();
				const nomeArquivo = this.form.get('nomeArquivo').value;
				this.uploadArquivo.tipo = TipoUploadEnum.NOVO;
				this.uploadArquivo.arquivoBase64 = file;
				this.uploadArquivo.nomeArquivo = nomeArquivo.split('.')[0];
				this.uploadArquivo.formatoArquivo = nomeArquivo.substring(
					nomeArquivo.lastIndexOf('.')
				);
				this.uploadArquivo.nomeArquivoApresentar = nomeArquivo;
				this.uploadArquivo.tituloModal = 'Upload de arquivo';
				this.openModalUploadArquivos();
			}
		});
	}

	buildForm(): void {
		this.form = this.fb.group({
			codigo: [null],
			titulo: [null, Validators.required],
			dataSolicitacao: new Date(),
			situacao: "PENDENTE",
			descricao: [null, Validators.required],
			arquivos: this.fb.array([] as UploadArquivo[]),
			fotoKeyUrlFull: [null],
			fotoKey: [null],
			motivoNegacao: [null],
			file: new FormControl(),
			nomeArquivo: new FormControl(),
		});
	}

	private getByIdSolicitacaoCompra() {
		this.admCoreApiSolicitacaoCompraService
			.getByIdSolicitacaoCompra(this.codigo)
			.subscribe({
				next: (response: any) => {
					this.zone.run(() => {
						const content =
							response && response.content ? response.content : {};

						const patchData = {
							...content
						};

						Object.keys(patchData).forEach((key) => {
							if (patchData[key] === undefined || patchData[key] === null) {
								delete patchData[key];
							}
						});

						this.form.patchValue(patchData);

						if (content.arquivos && content.arquivos.length > 0) {
							content.arquivos.forEach((a) => {
								this.documentosTableComponent.anexos.push({
									codigo: a.codigo,
									data: content.dataSolicitacao,
									anexo: a.urlFull,
									arquivoUpload: null,
									nomeArquivo: a.nome,
									formatoArquivo: a.extensao,
									observacao: a.observacao
								});
							});
							this.documentosTableComponent.loadData();
						}
						this.cd.detectChanges();
					});
				},
				error: () => {
					this.snotifyService.error(
						"Erro ao buscar solicitação de compra.",
						"Atenção"
					);
				},
			});
	}

	aprovarSolicitacao() {
		if (this.sessionService.temPermissaoAdm('10.10')) {
			this.form.controls['situacao'].setValue(
				'APROVADO'
			);
			this.save();
		} else {
			this.snotifyService.error('Você não possui a permissão 10.10 - Aprovar/Negar Solicitação de Compra');
		}
	}

	get approveMode(): boolean {
		return this.mode === "aprovar";
	}

	btnApproveHandler(item) {
		if (this.sessionService.temPermissaoAdm('10.10')) {
			this.openConfirmDisapproveModal(item);
		} else {
			this.snotifyService.error('Você não possui a permissão 10.10 - Aprovar/Negar Solicitação de Compra');
		}
	}

	get isSituacaoNegado(): boolean {
		return (this.form.controls["situacao"] && this.form.controls["situacao"].value &&
			(this.form.controls["situacao"].value === 'NEGADO'));
	}

	get isSituacaoPendente(): boolean {
		return (this.form.controls["situacao"] && this.form.controls["situacao"].value &&
			(this.form.controls["situacao"].value === 'PENDENTE'));
	}

	get isSituacaoAprovado(): boolean {
		return (this.form.controls["situacao"] && this.form.controls["situacao"].value &&
			(this.form.controls["situacao"].value === 'APROVADO'));
	}

	return() {
		this.router.navigate(["adm", "solicitacao-compra"]);
	}

	removeNullFields(obj: any): any {
		return Object.keys(obj).reduce((acc, key) => {
			if (obj[key] !== null && obj[key] !== undefined) {
				acc[key] = obj[key];
			}
			return acc;
		}, {});
	}

	get urlLog() {
		return this.admRest.buildFullUrlAdmCore(
			"solicitacao-compra/logs-solicitacao-compra"
		);
	}

	openConfirmDisapproveModal(codigo: string): void {
		const dialogRef = this.dialog.open(ModalConfirmDisapproveComponent, {
			width: "600px",
			height: "350px",
			autoFocus: false,
			data: { codigo: codigo },
		});

		dialogRef.afterClosed().subscribe((result) => {
			if (result) {
				this.form.controls['motivoNegacao'].setValue(result);
				this.form.controls['situacao'].setValue(
					'NEGADO'
				);
				this.save();
			}
		});
	}

	save() {
		if (this.form.invalid) {
			this.form.markAllAsTouched();
			this.snotifyService.error(
				"Preencha todos os campos obrigatórios",
				"Atenção"
			);
			return;
		}
		let formValue = this.form.getRawValue();
		if (this.documentosTableComponent && this.documentosTableComponent.anexos &&
			this.documentosTableComponent.anexos.length > 0) {
			this.documentosTableComponent.anexos.forEach(a => {
				formValue.arquivos.push({
					nome: a.nomeArquivo,
					extensao: a.formatoArquivo,
					dados: a.arquivoUpload,
					dataRegistro: new Date().getTime(),
					observacao: a.observacao
				});
			});
		}
		formValue = this.removeNullFields(formValue);

		this.admCoreApiSolicitacaoCompraService
			.saveSolicitacaoCompra(formValue)
			.subscribe({
				next: () => {
					this.snotifyService.success(
						"Operação realizada com sucesso"
					);
					this.return();
				},
				error: () => {
					const errorMessage = this.codigo
						? "Erro ao atualizar os dados."
						: "Erro ao cadastrar a solicitação de compra.";
					this.snotifyService.error(errorMessage, "Atenção");
				},
			});
	}

	documentosChangeEvent(documentos: any) {
		this.documentosAlunos = documentos;
	}

	editEvent(uploadArquivo: UploadArquivo) {
		this.uploadArquivo = uploadArquivo;
		this.openModalUploadArquivos();
	}

	openModalUploadArquivos() {
		if (this.uploadArquivo.tipo === TipoUploadEnum.NOVO) {
			this.uploadArquivo.sizeFileMb = this.getSizeFile(
				this.uploadArquivo.arquivoBase64
			);
			if (this.uploadArquivo.sizeFileMb > 8.0) {
				this.resetarFormularioUpload();
				this.snotifyService.warning(
					'limite maximo arquivo ultrapassado'
				);
				return;
			}
		}
		this.openModalUpload();
	}

	getSizeFile(file): number {
		let sizeFile = 0.0;
		if (file) {
			const fileBaseB4 = file.split(",")[1];
			if (fileBaseB4.endsWith("==")) {
				sizeFile = fileBaseB4.length * (3 / 4) - 2;
			} else {
				sizeFile = fileBaseB4.length * (3 / 4) - 1;
			}
			sizeFile = sizeFile / 1024 / 1024;
		}
		return Number(sizeFile.toFixed(2));
	}

	resetarFormularioUpload() {
		this.fileInputComponent.filename = null;
		this.fileInputComponent.controlFc.reset();
		this.fileInputComponent.control.reset();
		this.fileInputComponent.nomeControl.reset();
	}

	openModalUpload() {
		const modal = this.modalService.open(
			this.uploadArquivo.tituloModal,
			ModalSolicitacaoCompraUploadArquivosComponent,
			PactoModalSize.LARGE,
			"modal-upload-file"
		);
		modal.componentInstance.isPermitidolancamentoDeDocumento = true;
		modal.componentInstance.uploadArquivo = this.uploadArquivo;
		this.resetarFormularioUpload();

		modal.result
			.then((dados) => {
				if (dados) {
					if (this.documentosTableComponent && this.documentosTableComponent.anexos == null) {
						this.documentosTableComponent.anexos = [];
					}
					this.documentosTableComponent.anexos = [
						...this.documentosTableComponent.anexos.filter(a => a.nomeArquivo !== dados.nomeArquivo),
						dados
					];
					this.documentosTableComponent.loadData();
				}
			})
			.catch((error) => {
				console.log(error);
			});
	}

	redirectToCompra() {
		const dialogRef = this.dialogService.open(
			'',
			UnderConstructionComponent,
			undefined,
			'under-construction-modal'
		);
		dialogRef.componentInstance.asDialog = true;
		dialogRef.result
			.then((result) => {
				this.clientDiscoveryService
					.linkZw(this.sessionService.usuarioOamd, this.sessionService.empresaId)
					.subscribe((urlZw) => {
						const url = `${urlZw}&urlRedirect=uriCompra&solicitacaoCompra=${this.codigo}`;
						this.abrirPopup(url, 'Compra', 800, 600);
					});
			})
			.catch((err) => throwError(err));
	}

	abrirPopup(
		URL: string,
		nomeJanela: string,
		comprimento: number,
		altura: number
	) {
		const atributos = `width=${comprimento}, height=${altura},left=0,screenX=0,top=0,screenY=0,dependent=yes,menubar=no,toolbar=no,resizable=yes,scrollbars=yes`;
		const parameterCaracter = URL.includes("?") ? "&" : "?";
		const urlPopup = URL + parameterCaracter + "from=popup";
		const win = window.open(urlPopup, nomeJanela, atributos);
		const timer = setInterval(() => {
			if (win && win !== undefined && win.closed) {
				clearInterval(timer);
				this.goBackClient.emit();
			}
		}, 500);
	}
}
