@import "src/assets/scss/pacto/plataforma-import.scss";

:host {
	display: block;
	left: 0px;
	background-color: white;
	color: $pretoPri;
	flex-direction: column;
}

.center-aux {
	width: calc(100vw - 460px);
	max-width: 1230px;
	@media (max-width: $plataforma-breakpoint-large) {
		width: calc(100vw - 260px);
	}
}

.titulo-configuracao-ficha {
	display: flex;
	line-height: 1;
	cursor: pointer;
}

.subtitulo-configuracao-ficha {
	@extend .type-caption;
	color: #b4b7bb;
	padding-left: 20px;
}

.view-header {
	border-bottom: 1px solid $cinza02;
	display: block;
	justify-content: center;
	padding-top: 15px;
	margin-left: auto;
	margin-right: auto;

	.header {
		display: flex;
		flex-direction: column;
		justify-content: space-between;
	}
}

.row-header {
	display: flex;
	padding-top: 30px;
}

.action-buttons {
	display: flex;
	width: 50%;
	justify-content: flex-end;

	.space-action {
		display: flex;
		padding: 0px 12px;
		cursor: pointer;

		.description-action {
			padding-left: 5px;
			@extend .type-btn-bold;
		}
	}
}

.body-row {
	display: flex;
	justify-content: center;
	flex-grow: 1;
	margin-bottom: 16px;
}

.listagem-atividades {
	margin-bottom: 160px !important;
}

.footer {
	height: 70px;
	background-color: #eff2f7;
	bottom: 0;
	left: 0;
	position: fixed;
	width: 100%;

	box-shadow: 0px -2px 4px 0px #e4e5e6;

	.margin-right-16px {
		margin-right: 16px;
	}

	::ng-deep {
		&.btn-excluir-ficha button {
			border: 1px solid #fafafa;
			background: #fafafa;
			color: $hellboyPri;
		}

		&.pct-trash-2 {
			color: $hellboyPri !important;
		}

		&.btn-default button {
			border: 1px solid #fafafa;
			background: #fafafa;
			color: $azulPacto02;
		}

		&.pct-bookmark {
			color: $azulPacto02 !important;
		}

		&.pct-settings {
			color: $azulPacto02 !important;
		}

		&.pct-send {
			color: $azulPacto02 !important;
		}
	}
}

.title-notificacao {
	font-family: "Nunito Sans";
	font-size: 20px;
	line-height: 40px;
	padding: 32px 0px 32px 0px;
	margin-left: 16px;

	.principal {
		color: #51555a;
		font-weight: 400;
		display: block;
	}

	.secundario {
		color: #0380e3;
		font-weight: 600;
		display: block;
	}
}

.posicionamento-responsivo {
	::ng-deep#show-log {
		width: 50px;
		height: 43px;
	}
}

@media (max-width: 1200px) {
	.posicionamento-responsivo {
		bottom: 20px;
	}
}

@media (min-width: 1000px) {
	.posicionamento-responsivo {
		padding-right: 11%;
	}
}

@media (min-width: 1400px) {
	.posicionamento-responsivo {
		padding-right: 10%;
	}
}

@media (min-width: 1550px) {
	.posicionamento-responsivo {
		padding-right: 16%;
	}
}

@media (min-width: 1800px) {
	.posicionamento-responsivo {
		padding-right: 18%;
	}
}

::ng-deep .row {
	margin-left: 0 !important;
	margin-right: 0 !important;
}

.card-info-tabs-ficha {
	display: flex;
	padding-top: 7px;
	background-color: white;
	width: 100%;
	border-bottom: 1px solid $cinza02;
}

.ficha-tabs-alt {
	display: flex;
	width: 80%;
	flex-wrap: wrap;
	height: 32px;

	.ficha {
		@extend .type-h6;
		height: 32px;
		cursor: pointer;
		padding: 5px 10px 5px;
		margin-right: 4px;
		border: 1px solid #ccc;
		background-color: #fbfbfc;
		color: #9298a0;
		border-width: 1px 1px 0px 1px;
		border-top-left-radius: 4px;
		border-top-right-radius: 4px;
		text-align: center;
		width: 144px;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;

		&.selected {
			color: #51555a;
			background-color: $branco;
			border-width: 2px 2px 0px 2px;
			font-family: Nunito Sans;
			font-size: 16px;
			font-weight: 600;
		}
	}

	.ficha-nova {
		width: 32px;
		height: 32px;
		display: flex;
		justify-content: center;
		align-items: center;
		@extend .type-h6;
		cursor: pointer;
		border: 2px solid $azulimPri;
		background-color: $azulimPri;
		border-width: 2px 2px 0px 2px;
		border-top-left-radius: 3px;
		border-top-right-radius: 3px;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: clip;
		color: white;
		font-size: 14px;
	}
}
