<div class="card-info-tabs-ficha pl-3 mt-3">
	<div class="ficha-tabs">
		<div
			(click)="tabChangeHandler(index)"
			*ngFor="let ficha of fichas; let index = index"
			[ngClass]="{ selected: fichaSelecionadaIndex === index }"
			class="ficha"
			title="{{ ficha?.nome }}">
			{{ ficha?.nome }}
		</div>
		<div
			(click)="abrirModalAdicionarFicha()"
			*ngIf="
				permissaoProgramaTreino?.editar ||
				permissaoProgramaTreino?.incluir ||
				permissaoProgramaTreino?.total
			"
			class="ficha-nova"
			id="ficha-nova">
			<i class="pct pct-plus"></i>
		</div>
	</div>
</div>
<div *ngIf="existeFicha">
	<div class="body-row">
		<div class="center-aux pl-3 pr-3">
			<pacto-adicionar-atividade
				#listaAtividadesComponent
				(addAtividade)="addAtividade($event)"
				(validaFicha)="validaFichaHandler($event)"
				*ngIf="permissaoProgramaTreino?.editar"
				[origemFichaPredefinida]="false"></pacto-adicionar-atividade>
		</div>
	</div>
	<div class="body-row listagem-atividades">
		<div class="center-aux pl-3 pr-3">
			<pacto-lista-atividades
				[atividades]="ficha?.atividades"></pacto-lista-atividades>
		</div>
	</div>
	<footer
		class="footer d-flex justify-content-end align-items-center posicionamento-responsivo">
		<div class="margin-right-16px">
			<pacto-log [titulo]="nomeLog" [url]="logUrl"></pacto-log>
			<ng-template #nomeLog>
				<span>Programa</span>
			</ng-template>
		</div>
		<div class="margin-right-16px btn-excluir-ficha">
			<pacto-cat-button
				(click)="excluirFicha()"
				[apenasIcone]="true"
				[disabled]="!permissaoProgramaTreino?.editar"
				[icon]="'pct pct-trash-2'"
				[v2]="true"
				i18n-label="@@perfil-aluno-programa-editar:excluir"
				id="excluir-ficha"
				label="Excluir ficha"></pacto-cat-button>
		</div>
		<div class="margin-right-16px btn-default">
			<pacto-cat-button
				(click)="tornarFichaPredefinidaHandler()"
				[apenasIcone]="true"
				[disabled]="!permissaotornarpredefinido"
				[icon]="'pct pct-bookmark'"
				[v2]="true"
				i18n-label="@@perfil-aluno-programa-editar:preDefinir"
				id="pre-ficha"
				label="Predefinir ficha"></pacto-cat-button>
		</div>
		<div class="margin-right-16px btn-default">
			<pacto-cat-button
				(click)="enviarProgramaOutrosAlunos()"
				[apenasIcone]="true"
				[disabled]="!permissaoEnviarTreinoEmMassa"
				[icon]="'pct pct-send'"
				[v2]="true"
				i18n-label="@@perfil-aluno-programa-editar:enviar"
				id="enviar-programa"
				label="Enviar treino para outros alunos"></pacto-cat-button>
		</div>
		<div class="margin-right-16px btn-default">
			<pacto-cat-button
				(click)="editarFichaHandler()"
				[apenasIcone]="true"
				[icon]="'pct pct-settings'"
				[v2]="true"
				i18n-label="@@perfil-aluno-programa-editar:editar"
				id="configuracoes-da-ficha"
				label="Configurações da ficha"></pacto-cat-button>
		</div>
		<div>
			<pacto-cat-button
				*ngIf="botaoAprovarDesabilitado"
				(click)="aprovarProgramaHandler()"
				[icon]="'pct pct-check'"
				[v2]="true"
				i18n-label="@@perfil-aluno-programa-editar:aprovar-programa"
				id="aprovar-programa"
				label="Aprovar treino"></pacto-cat-button>
		</div>

		<div>
			<pacto-cat-button
				*ngIf="!botaoAprovarDesabilitado"
				(click)="salvarFichaHandler('salvar')"
				[disabled]="!permissaoProgramaTreino?.editar || !fichaSendoModificada"
				[icon]="'pct pct-check'"
				[v2]="true"
				i18n-label="@@adicionar-atividade:salvarFicha"
				id="salvar-ficha"
				label="Salvar Ficha"></pacto-cat-button>
		</div>
	</footer>
</div>

<div *ngIf="!existeFicha" class="title-notificacao">
	<span class="principal">
		Não foi cadastrada nenhuma ficha de treino para este programa. Que tal
		começar uma agora?
	</span>
	<span class="secundario">Clique em "+" para iniciar</span>
</div>

<pacto-modal-adicionar-atividades
	(fechar)="closeAtividade()"
	(validaFicha)="validaFichaHandler($event)"
	*ngIf="adicionandoAtividade"
	[aluno]="aluno"
	[ficha]="ficha"
	[programa]="programa"></pacto-modal-adicionar-atividades>

<pacto-modal-enviar-programa-alunos
	(fechar)="closeEnviarOutros()"
	*ngIf="enviandoOutrosAlunos"
	[codigoProgramaBase]="programa.id"></pacto-modal-enviar-programa-alunos>

<pacto-traducoes-xingling #notificacoesTranslate>
	<span xingling="ficha-edit-success">Ficha alterada com sucesso.</span>
	<span xingling="ficha-edit-error">
		Não foi possível salvar as alterações da ficha.
	</span>
	<span xingling="ficha-remove-success">Ficha excluida com sucesso.</span>
	<span xingling="ficha-create-success">Ficha criada com sucesso.</span>
	<span xingling="ficha-duplic-error">
		Já existe uma ficha cadastrada com o nome {{ nomeFicha }}.
	</span>
	<span xingling="ficha-predefinida-success">
		Ficha {{ nomeFicha }} foi adicionado a suas predefinidas.
	</span>
	<span xingling="usuariosempermissao">
		O usuário não possui permissão para Predefinir uma Ficha.
	</span>
</pacto-traducoes-xingling>
<pacto-traducoes-xingling #modalTranslate>
	<span xingling="ficha-remove-title">Excluir ficha</span>
	<span xingling="ficha-remove-body">
		Deseja excluir a ficha {{ nomeFicha }}?
	</span>
	<span xingling="ficha-remove-action">OK</span>
</pacto-traducoes-xingling>
