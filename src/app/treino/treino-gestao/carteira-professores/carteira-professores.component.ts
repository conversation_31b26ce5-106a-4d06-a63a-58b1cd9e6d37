import {
	Component,
	OnInit,
	ViewChild,
	TemplateRef,
	ChangeDetectorRef,
} from "@angular/core";

import { Observable } from "rxjs";
import { map } from "rxjs/operators";

import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import {
	PactoDataGridConfig,
	DataFiltro,
	RelatorioComponent,
	GridFilterConfig,
	GridFilterMany,
	GridFilterType,
	PactoDataGridButtonConfig,
	TraducoesXinglingComponent,
} from "ui-kit";
import { IndicadoresDetalhesModalComponent } from "./indicadores-detalhes-modal/indicadores-detalhes-modal.component";
import { TreinoApiColaboradorService } from "treino-api";
import { SessionService } from "@base-core/client/session.service";
import { RestService } from "@base-core/rest/rest.service";
import { ModalService } from "@base-core/modal/modal.service";

@Component({
	selector: "pacto-carteira-professores",
	templateUrl: "./carteira-professores.component.html",
	styleUrls: ["./carteira-professores.component.scss"],
})
export class CarteiraProfessoresComponent implements OnInit {
	@ViewChild("celulaProfessor", { static: false }) celulaProfessor;

	@ViewChild("nomeTitulo", { static: false }) nomeTitulo;
	@ViewChild("comTreinoTitulo", { static: false }) comTreinoTitulo;
	@ViewChild("semTreinoTitulo", { static: false }) semTreinoTitulo;
	@ViewChild("vencidosTitulo", { static: false }) vencidosTitulo;
	@ViewChild("proxVencimentoTitulo", { static: false }) proxVencimentoTitulo;
	@ViewChild("avaliacaoTitulo", { static: false }) avaliacaoTitulo;
	@ViewChild("estrelas2Titulo", { static: false }) estrelas2Titulo;

	// Filter values
	@ViewChild("professorLabel", { static: false }) professorLabel;
	@ViewChild("professor2Label", { static: false }) professor2Label;
	@ViewChild("statusLabel", { static: false }) statusLabel;
	@ViewChild("contratoLabel", { static: false }) contratoLabel;
	@ViewChild("situacaoProgramaLabel", { static: false }) situacaoProgramaLabel;
	@ViewChild("situacaoContratoLabel", { static: false }) situacaoContratoLabel;

	// Status labels
	@ViewChild("statusTranslator", { static: false }) statusTranslator;
	@ViewChild("programaTranslator", { static: false }) programaTranslator;
	@ViewChild("contratoTranslator", { static: false }) contratoTranslator;
	@ViewChild("relatorio", { static: false }) relatorio: RelatorioComponent;

	@ViewChild("titleModal", { static: true }) titleModal;
	@ViewChild("xingling", { static: true }) xingling: TraducoesXinglingComponent;
	@ViewChild("xinglingFilter", { static: true })
	xinglingFilter: TraducoesXinglingComponent;
	endpointShare: any;

	constructor(
		private modal: NgbModal,
		private modalShare: ModalService,
		private rest: RestService,
		private sessionService: SessionService,
		private cd: ChangeDetectorRef,
		private colaboradorService: TreinoApiColaboradorService
	) {}

	integracaoZW = false;

	table: PactoDataGridConfig;
	filterConfig: GridFilterConfig;
	ready = false;
	paramTitleModal: any = {};
	incluirProfessoresInativos = false;

	private professores: Array<any>;

	private indicadorEnum = {
		comTreino: "N_PROF_CARTEIRA",
		semTreino: "N_PROF_CARTEIRA_SEM_TREINO",
		vencidos: "N_PROF_CARTEIRA_VENCIDOS",
		proxVencimento: "N_PROF_CARTEIRA_PROX_VENCIMENTO",
		avaliacao: "N_PROF_CARTEIRA_MEDIA_AVALIACAO",
		estrelas2: "N_PROF_CARTEIRA_2_ESTRELAS",
	};

	ngOnInit() {
		this.integracaoZW = this.sessionService.integracaoZW;
		this.fetchFilterData().subscribe(() => {
			this.configTable();
			this.configFilters();
			this.ready = true;
			this.cd.detectChanges();
		});
	}

	cellClickHandler($event) {
		if ($event.column.nome !== "nome") {
			this.paramTitleModal.nomeColuna = $event.column.nome;
			this.paramTitleModal.nomeProfessor = $event.row.professor
				? $event.row.professor.nome
				: "Total";
			const filter = this.buildDetailModalFilter($event);
			const modalHandle = this.modal.open(IndicadoresDetalhesModalComponent, {
				size: "lg",
				windowClass: "modal-xl",
			});
			modalHandle.componentInstance.setBaseFiltersModal(
				filter,
				this.titleModal
			);
		}
	}

	alterConfigHandler(statusconfig: any) {
		if (statusconfig.clickConfigId === "incluirProfessorInativo") {
			this.incluirProfessoresInativos =
				!statusconfig.configsValue.incluirProfessorInativo;
			this.fetchFilterData().subscribe(() => {
				this.configFilters();
				this.ready = true;
			});
		}
	}

	private buildDetailModalFilter($event) {
		const filter = this.relatorio.fetchFiltros();
		const detailsFilter: DataFiltro = {
			filters: {},
		};
		const targetKeys = [
			"dataReferencia",
			"status",
			"situacaoContrato",
			"situacaoPrograma",
			"indicador",
		];
		if (filter.filters) {
			targetKeys.forEach((target) => {
				const value = filter.filters[target];
				if (value !== null && value !== undefined) {
					detailsFilter.filters[target] = value;
				}
			});
		}
		detailsFilter.filters.indicador = this.indicadorEnum[$event.column.nome];
		if ($event.row.professor) {
			detailsFilter.filters.professorId = $event.row.professor.id;
		} else {
			detailsFilter.filters.professoresIds = filter.filters.professoresIds;
		}

		// Make deep clone
		const json = JSON.stringify(detailsFilter);
		const clone = JSON.parse(json);

		return clone;
	}

	private fetchFilterData(): Observable<any> {
		const professores$ = this.colaboradorService
			.obterTodosColaboradores(this.incluirProfessoresInativos)
			.pipe(
				map((data) => {
					data.content.forEach((professor: any) => {
						professor.value = professor.id;
						professor.label = professor.nome;
					});
					this.professores = data.content;
					return true;
				})
			);
		return professores$;
	}

	private configTable() {
		this.endpointShare = this.rest.buildFullUrl(
			"professores/indicadores-carteira-professores"
		);
		this.table = new PactoDataGridConfig({
			endpointUrl: this.endpointShare,
			logUrl: this.rest.buildFullUrl(
				"log/listar-log-exportacao/indicadoresCarteiradosProfessores"
			),
			exportButton: false,
			pagination: false,
			totalRow: true,
			rowClick: false,
			columns: [
				{
					nome: "nome",
					titulo: this.xingling.getTemplate("nomeTitulo"),
					ordenavel: false,
					celula: this.xingling.getTemplate("celulaProfessor"),
					visible: true,
				},
				{
					nome: "comTreino",
					titulo: this.xingling.getTemplate("comTreinoTitulo"),
					visible: true,
					cellPointerCursor: true,
				},
				{
					nome: "semTreino",
					titulo: this.xingling.getTemplate("semTreinoTitulo"),
					visible: true,
					cellPointerCursor: true,
				},
				{
					nome: "vencidos",
					titulo: this.xingling.getTemplate("vencidosTitulo"),
					visible: true,
					cellPointerCursor: true,
				},
				{
					nome: "proxVencimento",
					titulo: this.xingling.getTemplate("proxVencimentoTitulo"),
					visible: true,
					cellPointerCursor: true,
				},
				{
					nome: "avaliacao",
					titulo: this.xingling.getTemplate("avaliacaoTitulo"),
					cellPointerCursor: true,
				},
				{
					nome: "estrelas2",
					titulo: this.xingling.getTemplate("estrelas2Titulo"),
					cellPointerCursor: true,
				},
			],
		});
	}

	private configFilters() {
		this.filterConfig = {
			filters: [
				{
					name: "professoresIds",
					label: this.xingling.getTemplate("professorLabel"),
					type: GridFilterType.MANY,
					options: this.professores,
				},
				{
					name: "status",
					label: this.xingling.getTemplate("statusLabel"),
					type: GridFilterType.MANY,
					translator: this.statusTranslator,
					options: [
						{ value: "ATIVO", label: this.xinglingFilter.getLabel("ativo") },
						{
							value: "VISITANTE",
							label: this.xinglingFilter.getLabel("visitante"),
						},
					],
				},
				{
					name: "situacaoPrograma",
					label: this.xingling.getTemplate("situacaoProgramaLabel"),
					type: GridFilterType.MANY,
					translator: this.programaTranslator,
					options: [
						{ value: "NOVOS", label: this.xinglingFilter.getLabel("novos") },
						{
							value: "RENOVADOS",
							label: this.xinglingFilter.getLabel("renovados"),
						},
					],
				},
			],
			configs: [
				{
					id: "incluirProfessorInativo",
					label: this.xingling.getTemplate("professor2Label"),
					cleanParams: ["professoresIds"],
				},
			],
		};
		if (this.integracaoZW) {
			this.filterConfig.filters.push({
				name: "situacaoContrato",
				label: this.xingling.getTemplate("situacaoContratoLabel"),
				type: GridFilterType.MANY,
				translator: this.contratoTranslator,
				options: [
					{
						value: "MATRICULA",
						label: this.xinglingFilter.getLabel("matricula"),
					},
					{
						value: "REMATRICULA",
						label: this.xinglingFilter.getLabel("rematricula"),
					},
					{
						value: "RENOVACAO",
						label: this.xinglingFilter.getLabel("renovacao"),
					},
				],
			});
			this.filterConfig.filters.forEach((filter: GridFilterMany) => {
				if (filter.name === "status") {
					filter.options.push(
						{
							value: "ATESTADO",
							label: this.xinglingFilter.getLabel("atestado"),
						},
						{
							value: "CANCELADO",
							label: this.xinglingFilter.getLabel("cancelado"),
						},
						{
							value: "CARENCIA",
							label: this.xinglingFilter.getLabel("carencia"),
						},
						{
							value: "DESISTENTE",
							label: this.xinglingFilter.getLabel("desistente"),
						},
						{
							value: "TRANCADO",
							label: this.xinglingFilter.getLabel("trancado"),
						},
						{
							value: "VENCIDO",
							label: this.xinglingFilter.getLabel("vencido"),
						},
						{ value: "OUTROS", label: this.xinglingFilter.getLabel("outros") }
					);
				}
			});
		} else {
			this.filterConfig.filters.forEach((filter: GridFilterMany) => {
				if (filter.name === "status") {
					filter.options.push({
						value: "INATIVO",
						label: this.xinglingFilter.getLabel("inativo"),
					});
				}
			});
		}
	}
}
