<div class="detail-data-container detail-data-container-general-info">
	<div class="pacto-grid">
		<div class="detail-data">
			<div class="detail-data-title">
				<span i18n="@@contract-detail:contract-payed-value">Valor pago</span>
			</div>
			<div class="detail-data-value">
				<span>{{ contract?.valorPago || 0 | currency : "BRL" }}</span>
			</div>
		</div>
		<div class="detail-data">
			<div class="detail-data-title">
				<span i18n="@@contract-detail:resp-lancamento-contrato">
					Resp. lançamento do contrato
				</span>
			</div>
			<div class="detail-data-value">
				<span>{{ contract?.responsavelLancamento }}</span>
			</div>
		</div>
		<div class="detail-data">
			<div class="detail-data-title">
				<span i18n="@@contract-detail:contract-payment-term">
					Condição de pagamento
				</span>
			</div>
			<div class="detail-data-value">
				<span>{{ contract?.condicaoDePagamento || "-" }}</span>
			</div>
		</div>
		<div class="detail-data">
			<div class="detail-data-title">
				<span i18n="@@contract-detaul:contract-dia-vencimento">
					Dia do vencimento
				</span>
			</div>
			<div class="detail-data-value">
				<span>
					{{ contract?.contratoRecorrencia?.diaVencimentoCartao || "-" }}
				</span>
			</div>
		</div>
		<div class="detail-data">
			<div class="detail-data-title">
				<span i18n="@@contract-detail:contract-base-value">Valor base</span>
			</div>
			<div class="detail-data-value">
				<span>{{ contract?.valorBaseCalculo || 0 | currency : "BRL" }}</span>
			</div>
		</div>
		<div class="detail-data">
			<div class="detail-data-title">
				<span i18n="@@contract-detail:contract-lancamento">Lançamento</span>
			</div>
			<div class="detail-data-value">
				<span>{{ contract?.dataLancamento || "-" | date : "dd/MM/yyyy" }}</span>
			</div>
		</div>
		<div class="detail-data">
			<div class="detail-data-title">
				<span i18n="@@contract-detail:contract-duration">Duração</span>
			</div>
			<div class="detail-data-value">
				<span i18n="@@contract-detail:contract-duration-months">
					{{ contract?.duracao }}
				</span>
			</div>
		</div>
		<div class="detail-data">
			<div class="detail-data-title">
				<span i18n="@@contract-detail:contract-type">Tipo de contrato</span>
				<i
					(click)="editTipoContrato()"
					[ngClass]="{
						'pct-save': editableTipoContrato,
						'pct-edit': !editableTipoContrato
					}"
					class="pct"></i>
				<pacto-cat-form-select
					*ngIf="editableTipoContrato"
					[control]="formGroup.get('tipoContrato')"
					[items]="tipoContratos"></pacto-cat-form-select>
			</div>
			<div *ngIf="!editableTipoContrato" class="detail-data-value">
				<span>{{ contract?.tipoContrato }}</span>
			</div>
		</div>
		<div class="detail-data">
			<div class="detail-data-title">
				<span i18n="@@contract-detail:contract-timetable">Horário</span>
			</div>
			<div class="detail-data-value">
				<span>{{ contract?.descricaoHorario || "-" }}</span>
			</div>
		</div>
		<div class="detail-data">
			<div class="detail-data-title">
				<span i18n="@@contract-detail:contract-data-termino-original">
					Data término original
				</span>
			</div>
			<div class="detail-data-value">
				<span>{{ contract?.vigenciaAte | date : "dd/MM/yyyy" }}</span>
			</div>
		</div>
		<div class="detail-data">
			<div class="detail-data-title">
				<span i18n="@@contract-detail:automatic-renewal">Bolsa</span>
			</div>
			<div class="detail-data-value">
				<ng-container *ngIf="contract?.plano?.bolsa">
					<span
						*ngIf="contract?.bolsa; else noBolsa"
						i18n="@@contract-detail:contract-bolsa-yes">
						Sim
					</span>
					<ng-template #noBolsa>
						<span i18n="@@contract-detail:contract-bolsa-no">Não</span>
					</ng-template>
				</ng-container>
				<ng-container *ngIf="!contract?.plano?.bolsa">-</ng-container>
			</div>
		</div>
		<div class="detail-data">
			<div class="detail-data-title">
				<span i18n="@@contract-detail:contrato-consultor-responsavel">
					Consultor responsável
				</span>
				<ng-container *ngIf="apresentarCampoAlterarConsultor">
					<i
						(click)="editNomeConsultorReponsavel()"
						[ngClass]="{
							'pct-save': editableNomeConsultorReponsavel,
							'pct-edit': !editableNomeConsultorReponsavel
						}"
						class="pct"></i>
					<pacto-cat-form-select
						*ngIf="editableNomeConsultorReponsavel"
						[control]="formGroup.get('nomeConsultorReponsavel')"
						[items]="consultores"></pacto-cat-form-select>
				</ng-container>
			</div>
			<div *ngIf="!editableNomeConsultorReponsavel" class="detail-data-value">
				<span>{{ contract?.nomeConsultorReponsavel }}</span>
			</div>
		</div>
		<div *ngIf="contract?.grupoDesconto?.descricao" class="detail-data">
			<div class="detail-data-title">
				<span i18n="@@contract-detail:grupo-desconto">Grupo com desconto</span>
			</div>
			<div class="detail-data-value">
				<span>
					{{ contract?.grupoDesconto?.descricao }}
				</span>
			</div>
		</div>

		<div class="detail-data">
			<div class="detail-data-title">
				<span i18n="@@contract-detail:direito-uso-concedido">
					Direito de uso concedido
				</span>
			</div>
			<div *ngIf="contract.pessoaOriginal" class="detail-data-value">
				<span *ngIf="opcoesContrato?.recuperarDireitoUso">
					{{ contract?.pessoaDTO?.nome }}
				</span>
				<span *ngIf="!opcoesContrato?.recuperarDireitoUso">
					{{ contract?.pessoaOriginal?.nome }}
					<i class="pct pct-chevron-right"></i>
					{{ contract?.pessoaDTO?.nome }}
				</span>
			</div>
			<div *ngIf="!contract.pessoaOriginal" class="detail-data-value">-</div>
		</div>
		<div *ngIf="contract?.dataAlteracaoManual" class="detail-data">
			<div class="detail-data-title">
				<span i18n="@@contract-detail:alteracao-data-base">
					Alteração data base
				</span>
			</div>
			<div class="detail-data-value">
				<span>
					{{ contract?.dataAlteracaoManual | date : "dd/MM/yyyy" }}
				</span>
			</div>
		</div>
		<div *ngIf="apresentarRenovavelAutomaticamente" class="detail-data">
			<div class="detail-data-title">
				<span i18n="@@contract-detail:automatic-renewal">
					Renovação automática
				</span>
				<i
					(click)="editPermiteRenovacaoAutomatica()"
					[ngClass]="{
						'pct-save': editablePermiteRenovacaoAutomatica,
						'pct-edit': !editablePermiteRenovacaoAutomatica
					}"
					class="pct"></i>
				<pacto-cat-form-select
					*ngIf="editablePermiteRenovacaoAutomatica"
					[control]="formGroup.get('permiteRenovacaoAutomatica')"
					[items]="renovacaoAutomaticaOptions"
					idKey="value"></pacto-cat-form-select>
			</div>
			<div
				*ngIf="!editablePermiteRenovacaoAutomatica"
				class="detail-data-value">
				<span
					*ngIf="contract?.permiteRenovacaoAutomatica; else noAutoRenewal"
					i18n="@@contract-detail:automatic-renewal-yes">
					Sim
				</span>
				<ng-template #noAutoRenewal>
					<span i18n="@@contract-detail:automatic-renewal-no">Não</span>
				</ng-template>
			</div>
		</div>
		<div *ngIf="contract?.observacao" class="detail-data">
			<div class="detail-data-title">
				<span i18n="@@contract-detail:observacao">Observação</span>
			</div>
			<div class="detail-data-value">
				<span>{{ contract?.observacao }}</span>
			</div>
		</div>
		<div class="detail-data">
			<div class="detail-data-title">
				<span i18n="@@contract-detail:contract-company">Empresa</span>
			</div>
			<div class="detail-data-value">
				<span>{{ contract?.nomeEmpresa }}</span>
			</div>
		</div>
		<div *ngIf="contract?.origemSistema !== 1" class="detail-data">
			<div class="detail-data-title">
				<span i18n="@@contract-detail:direito-uso-concedido-para-desconto">
					Origem
				</span>
			</div>
			<div class="detail-data-value">
				<span>
					{{ textoOrigemContrato }}
				</span>
			</div>
		</div>
		<div *ngIf="contract?.convenioDesconto?.descricao" class="detail-data">
			<div class="detail-data-title">
				<span i18n="@@contract-detail:convenio-desconto">
					Convênio de desconto
				</span>
			</div>
			<div class="detail-data-value">
				<span>
					{{ contract?.convenioDesconto?.descricao }}
				</span>
			</div>
		</div>
	</div>

	<div class="detalhamento-credito-info">
		<hr />
		<div class="pacto-grid">
			<div class="detail-data">
				<div class="detail-data-title">
					<span i18n="@@contract-detail:contract-qtd-credito-compra">
						Qnt. {{ nomenclaturaVendaCredito }} Compra
					</span>
				</div>
				<div class="detail-data-value">
					<span>
						{{
							contract?.contratoDuracao?.contratoDuracaoCreditoTreino
								?.quantidadeCreditoCompra || 0
						}}
					</span>
				</div>
			</div>

			<div class="detail-data">
				<div class="detail-data-title">
					<span i18n="@@contract-detail:contrato-qtd-saldo">
						Quantidade de saldo
					</span>
				</div>
				<div class="detail-data-value">
					<span>{{ saldoCredito }}</span>
				</div>
			</div>

			<div class="detail-data">
				<div class="detail-data-title">
					<span i18n="@@contract-detail:contract-marcacoes-futuras">
						Marcações futuras
					</span>
				</div>
				<div class="detail-data-value">
					<span>
						{{ marcacoesFuturas }}
					</span>
				</div>
			</div>

			<div class="detail-data">
				<div class="detail-data-title">
					<span i18n="@@contract-detail:contract-qtd-mensal">
						Quantidade mensal
					</span>
				</div>
				<div class="detail-data-value">
					<span>
						{{
							contract?.contratoDuracao?.contratoDuracaoCreditoTreino
								?.creditoTreinoNaoCumulativo
								? contract?.contratoDuracao?.contratoDuracaoCreditoTreino
										?.quantidadeCreditoMensal || 0
								: "-"
						}}
					</span>
				</div>
			</div>

			<div class="detail-data">
				<div class="detail-data-title">
					<span i18n="@@contract-detail:contrato-vezes-semana">
						Vezes por semana
					</span>
				</div>
				<div class="detail-data-value">
					<span>
						{{
							!contract?.vendaCreditoSessao
								? contract?.contratoDuracao?.contratoDuracaoCreditoTreino
										?.numeroVezesSemana || 0
								: "-"
						}}
					</span>
				</div>
			</div>
		</div>
	</div>
</div>
