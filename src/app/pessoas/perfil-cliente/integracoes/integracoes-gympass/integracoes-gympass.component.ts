import {
	ChangeDetectorRef,
	Component,
	Input,
	OnInit,
	ViewChild,
} from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { ActivatedRoute } from "@angular/router";
import { SessionService } from "@base-core/client/session.service";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { AdmLegadoTelaClienteService } from "adm-legado-api";
import { SnotifyService } from "ng-snotify";
import { PactoDataGridConfig, RelatorioComponent } from "ui-kit";
import * as moment from "moment";
import { RestService } from "@base-core/rest/rest.service";
import {
	AdmCoreApiClienteService,
	ClienteDadosPessoais,
	AdmCoreApiIntegracoesService,
	ConfiguracaoIntegracaoGymPass,
} from "adm-core-api";

@Component({
	selector: "pacto-integracoes-gympass",
	templateUrl: "./integracoes-gympass.component.html",
	styleUrls: ["./integracoes-gympass.component.scss"],
})
export class IntegracoesGympassComponent implements OnInit {
	@Input()
	dadosPessoais: ClienteDadosPessoais;

	tableData: PactoDataGridConfig;
	@ViewChild("tableDataRef", { static: false })
	tableDataRef: RelatorioComponent;
	@ViewChild("celulaLegenda", { static: true })
	public celulaLegenda;
	matricula;
	temToken = false;
	configuracao: ConfiguracaoIntegracaoGymPass;
	empresaTemConfiguracao: boolean = false;
	alunoTemDados: boolean = false;
	form = new FormGroup({
		tipo: new FormControl(1),
		token: new FormControl("", Validators.required),
	});
	tiposTokenGympass = [
		{
			id: 1,
			label: "Tipo 1",
		},
		{
			id: 2,
			label: "Tipo 2",
		},
		{
			id: 3,
			label: "Tipo 3",
		},
		{
			id: 4,
			label: "Tipo 4",
		},
		{
			id: 5,
			label: "Tipo 5",
		},
	];

	constructor(
		private route: ActivatedRoute,
		private dialog: NgbActiveModal,
		private sessionService: SessionService,
		private msAdmCoreService: AdmCoreApiClienteService,
		private msIntegracoesService: AdmCoreApiIntegracoesService,
		private telaClienteService: AdmLegadoTelaClienteService,
		private snotifyService: SnotifyService,
		private cd: ChangeDetectorRef,
		private rest: RestService
	) {}

	ngOnInit() {
		this.matricula = this.route.snapshot.params["aluno-matricula"];
		this.msAdmCoreService.dadosPessoais(this.matricula).subscribe(
			(dados) => {
				this.dadosPessoais = dados;
				this.consultarConfiguracao();
				this.initTable();
				this.initDadosGympassCliente();
				this.obterUniqueGymPass();
				this.cd.detectChanges();
			},
			(error) => {
				this.snotifyService.error(error.error.meta.message);
			}
		);
	}

	initDadosGympassCliente() {
		this.msAdmCoreService
			.historicoGympass(this.dadosPessoais.codigoPessoa)
			.subscribe((response) => {
				if (response.totalElements) {
					this.alunoTemDados = response.totalElements > 0;
					this.cd.detectChanges();
				}
			});
	}

	consultarConfiguracao() {
		this.msIntegracoesService
			.configuracaoIntegracaoGympass(this.sessionService.empresaId)
			.subscribe(
				(config) => {
					this.configuracao = config;
					this.empresaTemConfiguracao =
						this.configuracao.codigoGympass &&
						this.configuracao.codigoGympass.length > 0;
					this.cd.detectChanges();
				},
				(error) => {
					console.error(error);
					this.snotifyService.error(error.error.meta.message);
				}
			);
	}

	obterUniqueGymPass() {
		this.telaClienteService
			.obterUniqueGympass(
				this.sessionService.chave,
				this.dadosPessoais.codigoPessoa
			)
			.subscribe(
				(response) => {
					this.temToken =
						response.content.token && response.content.token.length > 0;
					this.form.get("token").setValue(response.content.token);
					this.form.get("tipo").setValue(response.content.type);
					this.cd.detectChanges();
				},
				(httpResponseError) => {
					console.log(httpResponseError);
				}
			);
	}

	salvarGympass() {
		if (!this.form.valid) {
			this.snotifyService.error("Informe o token do gympass!");
			return;
		}
		this.telaClienteService
			.cadastrarGympass(
				this.sessionService.chave,
				this.dadosPessoais.codigoPessoa,
				this.sessionService.empresaId,
				this.sessionService.loggedUser.usuarioZw,
				this.form.get("tipo").value,
				this.form.get("token").value
			)
			.subscribe(
				(response) => {
					this.snotifyService.success("Token salvo com sucesso!");
					this.dialog.close();
				},
				(httpResponseError) => {
					this.snotifyService.error(httpResponseError.error.meta.message);
				}
			);
	}

	excluirGympass() {
		if (this.form.get("token").value && this.form.get("token").value !== "") {
			this.telaClienteService
				.excluirGympass(
					this.sessionService.chave,
					this.dadosPessoais.codigoPessoa,
					this.sessionService.empresaId,
					this.sessionService.loggedUser.usuarioZw
				)
				.subscribe(
					(response) => {
						this.obterUniqueGymPass();
						this.snotifyService.success("Token excluído com suceso!");
						this.dialog.close();
						this.cd.detectChanges();
					},
					(httpResponseError) => {
						this.snotifyService.error(httpResponseError.error.meta.message);
					}
				);
		}
	}

	// autorizarGympass() {
	//     this.telaClienteService
	//         .autorizarGympass(
	//             this.sessionService.chave,
	//             this.codPessoa,
	//             this.sessionService.empresaId,
	//             this.sessionService.loggedUser.usuarioZw
	//         )
	//         .subscribe(
	//             response => {
	//                 this.notificationService.success(
	//                     'Token excluído com suceso!'
	//                 );
	//                 this.dialog.close();
	//             },
	//             httpResponseError => {
	//                 this.notificationService.error(
	//                     httpResponseError.error.meta.message
	//                 );
	//             }
	//         );
	// }

	private initTable() {
		this.tableData = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrlAdmCore(
				`clientes/${this.dadosPessoais.codigoPessoa}/historico-acessos-gympass`
			),
			quickSearch: false,
			ghostLoad: true,
			ghostAmount: 3,
			showFilters: false,
			columns: [
				{
					nome: "codigo",
					titulo: "Código",
					visible: true,
					ordenavel: true,
				},
				{
					nome: "dataAcesso",
					titulo: "Data de acesso",
					visible: true,
					ordenavel: true,
					valueTransform(v) {
						return moment(v).format("DD/MM/YYYY");
					},
				},
				{
					nome: "token",
					titulo: "Token",
					visible: true,
					ordenavel: true,
				},
				{
					nome: "legenda",
					titulo: "Legenda",
					visible: true,
					ordenavel: true,
					celula: this.celulaLegenda,
				},
				{
					nome: "valor",
					titulo: "Valor",
					visible: true,
					ordenavel: true,
					valueTransform(v = 0) {
						return v.toLocaleString("pt-BR", {
							style: "currency",
							currency: "BRL",
						});
					},
				},
			],
		});
	}
}
