import {
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	ElementRef,
	Input,
	OnDestroy,
	OnInit,
	QueryList,
	ViewChild,
	ViewChildren,
} from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import { ActivatedRoute, ParamMap, Router } from "@angular/router";
import { SessionService } from "@base-core/client/session.service";
import { ModalService } from "@base-core/modal/modal.service";
import { LocalStorageSessionService } from "@base-core/rest/local-storage-session.service";
import { SessionService as SessionServiceSdk } from "sdk";
import {
	AdmCoreApiClienteMensagemService,
	AdmCoreApiClienteRestricaoService,
	AdmCoreApiClienteService,
	AdmCoreApiContratoService,
	AdmCoreApiNegociacaoService,
	AdmCoreApiObservacaoService,
	Biometria,
	ClienteDadosAuxiliares,
	ClienteDadosPessoais,
	ClienteDadosPlano,
	ClienteRestricao,
	Importacao,
	TipoClienteRestricaoEnum,
} from "adm-core-api";
import {
	AdmLegadoAutorizarAcessoService,
	AdmLegadoTelaClienteService,
	ApiResponseList,
} from "adm-legado-api";
import { AdmMsApiAlunosFavoritosService } from "adm-ms-api";
import { SnotifyService } from "ng-snotify";
import { combineLatest, Observable, of, Subject, Subscription } from "rxjs";
import {
	catchError,
	concatMap,
	map,
	startWith,
	switchMap,
} from "rxjs/operators";
import { TreinoBiStateService } from "src/app/treino/treino-bi/components/treino-bi-home-v2/treino-bi-state.service";
import {
	PerfilAcessoFuncionalidadeNome,
	PerfilAcessoRecursoNome,
	TreinoApiAlunosService,
} from "treino-api";
import {
	DialogAutorizacaoAcessoComponent,
	DialogService,
	PactoModalRef,
	PactoModalSize,
	TraducoesXinglingComponent,
} from "ui-kit";
import { ClientDiscoveryService } from "../../../microservices/client-discovery/client-discovery.service";
import { ModalConvidadosComponent } from "../modal-convidados/modal-convidados.component";
import { ModalCobrancaAutomaticaComponent } from "../pacto-pay/autorizacao-de-cobranca/modal-cobranca-automatica/modal-cobranca-automatica.component";
import { ModalArmarioComponent } from "../perfil-cliente-header/modal-armario/modal-armario.component";
import { ModalAvisoAlunoComponent } from "../perfil-cliente-header/modal-aviso-aluno/modal-aviso-aluno.component";
import { AlterarMatriculaComponent } from "./alterar-matricula/alterar-matricula.component";
import { AvisosComponent } from "./avisos/avisos.component";
import { LinhaDoTempoComponent } from "./linha-do-tempo/linha-do-tempo.component";
import { ModalAvisoConsultorComponent } from "./modal-aviso-consultor/modal-aviso-consultor.component";
import { ModalBloqueioAcessoCatracaComponent } from "./modal-bloqueio-acesso-catraca/modal-bloqueio-acesso-catraca.component";
import { ModalDefinirSenhaAcessoComponent } from "./modal-definir-senha-acesso/modal-definir-senha-acesso.component";
import { ModalImagemComponent } from "./modal-imagem/modal-imagem.component";
import { ModalObjetivoAlunoComponent } from "./modal-objetivo-aluno/modal-objetivo-aluno.component";
import { ModalRegistrarAcessoManualComponent } from "./modal-registrar-acesso-manual/modal-registrar-acesso-manual.component";
import { ModalShareLinkClienteComponent } from "./modal-share-link-cliente/modal-share-link-cliente.component";
import { ObservacoesComponent } from "./observacoes/observacoes.component";
import {
	isNullOrUndefinedOrEmpty,
	PerfilRecursoPermissoTipo,
	PlataformaModulo,
} from "sdk";
import { ModalCadastroStatusComponent } from "./modal-cadastro-status/modal-cadastro-status.component";
import { ModalAtestadoAptidaoFisicaComponent } from "./modal-atestado-aptidao-fisica/modal-atestado-aptidao-fisica.component";
import { ModalCobrancaComponent } from "./modal-cobranca/modal-cobranca.component";
import { AlunoAppInfo } from "../../../../../projects/treino-api/src/lib/aluno.model";
import { ModalMensagemProdutoVencidoComponent } from "./modal-mensagem-produto-vencido/modal-mensagem-produto-vencido.component";
import { LayoutNavigationService, PermissaoService } from "pacto-layout";
import { ModalNivelClienteComponent } from "./modal-nivel-cliente/modal-nivel-cliente.component";
import { ModalObjecaoDefinitivaComponent } from "../perfil-cliente-header/modal-objecao-definitiva/modal-objecao-definitiva.component";
import { ModalContatoClienteComponent } from "../perfil-cliente-header/modal-contato-cliente/modal-contato-cliente.component";
import { CobrancaService } from "../../../cobranca/cobranca.service";
import { ModalContratosRenovarERematricularComponent } from "../contratos/components/modal-contratos-renovar-e-rematricular/modal-contratos-renovar-e-rematricular.component";
import { TreinoConfigCacheService } from "../../../base/configuracoes/configuration.service";
import { ModalHistoricoReposicaoAulasColetivasComponent } from "./modal-historico-reposicao-aulas-coletivas/modal-historico-reposicao-aulas-coletivas.component";
import { MatDialog } from "@angular/material";
import { ModalClienteRestricaoComponent } from "./modal-cliente-restricao/modal-cliente-restricao.component";
import { NotificarClienteRestricaoService } from "./modal-cliente-restricao/notificar-cliente-restricao.service";
import { IncluirParcelasLinkModalComponent } from "./modal-incluir-parcelas-link/incluir-parcelas-link-modal.component";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { MdlClienteResumoContratoComponent } from "../modals/mdl-cliente-resumo-contrato/mdl-cliente-resumo-contrato.component";
import { PerfilClienteStateNotifierService } from "./services/perfil-cliente-state-notifier.service";
import { ModalHistoricoCarteiraComponent } from "../perfil-cliente-header/modal-historico-carteira/modal-historico-carteira.component";
import { PactoPrintService } from "../../../../../projects/zw-servlet-api/src/lib/processos/pacto-print.service";
import { DatePipe } from "@angular/common";

declare var moment;

@Component({
	selector: "pacto-perfil-cliente-header-v2",
	templateUrl: "./perfil-cliente-header-v2.component.html",
	styleUrls: ["./perfil-cliente-header-v2.component.scss"],
})
export class PerfilClienteHeaderV2Component
	implements OnInit, OnDestroy, AfterViewInit
{
	@Input() isMiniPerfil: boolean = false;
	isPerfilExpandido: boolean = true;
	isSubMenuVendasExpandido: boolean = false;
	isSubMenuCompartilharLinkExpandido: boolean = false;
	isSubMenuContaCorrenteExpandido: boolean = false;
	isSubMenuClubeVantagensExpandido: boolean = false;
	isSubMenuLancarAvisoExpandido: boolean = false;
	isSubMenuRelacionamentoExpandido: boolean = false;
	isSubMenuIntegracoesExpandido: boolean = false;
	isSubMenuCarteirinhaExpandido: boolean = false;
	private isProcessingCarteirinha: boolean = false;
	matricula: string;
	codigoCliente: string;
	codigoPessoa;
	pontos = 0;
	saldo = 0.0;
	aberto = true;
	dadosPessoais: ClienteDadosPessoais;
	alunoAppInfo: AlunoAppInfo;
	usuarioVerificacao: Importacao;
	assinatura: Biometria;
	aluno;
	modalidades = [];
	botaoTransferenciaSaldo: string = "Transferir saldo";
	botaoAjustarSaldo: string = "Ajustar saldo";
	isPossuiRestricao: boolean = false;
	isPossuiRestricaoPorInadimplencia: boolean = false;
	permiteIncluirOrRetirirClienteRestricao: boolean = false;
	clienteRestricoes: ClienteRestricao[] = [];

	dadosPlano: ClienteDadosPlano;
	dadosAuxiliares: ClienteDadosAuxiliares;
	form: FormGroup = new FormGroup({
		liberacaoCatacra: new FormControl(true),
	});

	updateObservations$ = new Subject<void>();
	updateWarnings$ = new Subject<void>();

	avisos = [];
	observacaoList!: any[];
	dadosPagamentoCliente: {
		bloqueioCobrancaAutomaticaData: string;
		bloqueioCobrancaAutomaticaTipo: number;
		convenioCobranca: string;
		hintExibir: string;
	};
	cobrancaAutorizada;
	mqv: any;
	apresentarBtnMQV = false;
	mgb: any;
	apresentarBtnSyncMGB = false;

	@ViewChild("traducoes", { static: true })
	traducoes: TraducoesXinglingComponent;

	@ViewChildren("statusBadge") statusBadges: QueryList<
		ElementRef<HTMLElement>
	> = new QueryList();

	infoConvidado;
	planosAtivos;

	dataMatricula: Date;
	dataVinculoAtual: Date;
	cacCliente: any;
	dataMatriculaHoje: boolean = false;
	permissaoCliente2_04: any;
	permissaoCliente2_29: any;
	permissaoCliente2_36: any;
	permissaoCliente9_50: any;
	permissaoCliente9_74: any;
	observacoesV1: any[] = [];
	permissaoCard: boolean = false;
	permissaoAvisos: boolean = false;
	permissaoVerificarCliente: boolean = false;
	permissaoCliente2_30: any;
	temPermissaoVerAbaContrato: any;
	saldoClasse: string;
	existeFluxoGymbot: boolean = false;
	existeFluxoGymbotPro: boolean = false;
	ativarAcessoConvidado: boolean = false;
	public ableNovoContrato: boolean = true;
	private subscriptionReloadDadosSituacoes: Subscription;
	processedAvisos: any[] = [];
	apresentarMenuContrato: boolean = false;
	canObservation: boolean = false;
	private permissaoLog: boolean = false;

	constructor(
		private activatedRoute: ActivatedRoute,
		private pactoModal: ModalService,
		private readonly dialogService: DialogService,
		private localStorageService: LocalStorageSessionService,
		private msAdmCoreService: AdmCoreApiClienteService,
		private cd: ChangeDetectorRef,
		private alunoService: TreinoApiAlunosService,
		private alunoFavoritoRecenteService: AdmMsApiAlunosFavoritosService,
		private state: TreinoBiStateService,
		private notificationService: SnotifyService,
		private router: Router,
		private clientDiscoveryService: ClientDiscoveryService,
		private sessionService: SessionService,
		private sessionServiceSdk: SessionServiceSdk,
		private autorizarAcessoService: AdmLegadoAutorizarAcessoService,
		private readonly admLegadoTelaClienteService: AdmLegadoTelaClienteService,
		private admContratoService: AdmCoreApiContratoService,
		private admCoreApiNegociacaoService: AdmCoreApiNegociacaoService,
		private mensagensAvisoService: AdmCoreApiClienteMensagemService,
		private layoutNavigationService: LayoutNavigationService,
		private permissaoService: PermissaoService,
		private configCache: TreinoConfigCacheService,
		private cobrancaService: CobrancaService,
		private admCoreApiClienteRestricaoService: AdmCoreApiClienteRestricaoService,
		private notificarClienteRestricaoService: NotificarClienteRestricaoService,
		private modal: NgbModal,
		private matDialog: MatDialog,
		private perfilClienteStateNotifierService: PerfilClienteStateNotifierService,
		private pactoPrintService: PactoPrintService,
		private admCoreObsService: AdmCoreApiObservacaoService,
		private datePipe: DatePipe
	) {}

	ngOnInit() {
		this.cd.detectChanges();
		// ajuste para recarregar o componente sempre que houver alteração no parametro na url
		this.activatedRoute.paramMap
			.pipe(
				switchMap((params: ParamMap) => {
					return this.verificarAlteracoesParam(params);
				})
			)
			.subscribe(() => {});

		this.activatedRoute.queryParams.subscribe((v) => {
			if (this.activatedRoute.snapshot.queryParams["reload"]) {
				this.loadData();
			}
		});

		this.subscriptionReloadDadosSituacoes =
			this.perfilClienteStateNotifierService.reloadDataSituacoes$.subscribe(
				() => {
					this.loadDadosPessoais();
					this.loadDadosPlano();
				}
			);

		this.updateObservations$
			.pipe(switchMap(() => this.msAdmCoreService.dadosPlano(this.matricula)))
			.subscribe((plano) => {
				this.dadosPlano = plano;
				this.preencherDataVinculoAtual();
				this.cd.detectChanges();
			});
		this.updateWarnings$
			.pipe(
				switchMap(() =>
					this.mensagensAvisoService.obterTodasMensagens(
						this.dadosPessoais.codigoPessoa
					)
				)
			)
			.subscribe((res: any) => {
				this.avisos = res.content;
				this.getAvisos();
				this.getObservacao();
				this.cd.detectChanges();
			});

		this.existeFluxoGymbot = false;
		if (this.sessionService.empresaId) {
			this.mensagensAvisoService
				.existeFluxoTelaClienteByEmpresa(this.sessionService.empresaId)
				.subscribe((response) => {
					this.existeFluxoGymbot = response.content;
				});
		}

		this.existeFluxoGymbotPro = false;
		if (this.sessionService.empresaId) {
			this.mensagensAvisoService
				.existeFluxoGymbotProTelaClienteByEmpresa(this.sessionService.empresaId)
				.subscribe((response) => {
					this.existeFluxoGymbotPro = response.content;
				});
		}

		this.possuiPermissaoIncluirOrRetirirClienteRestricao();
		this.permissaoLog = this.validarPermissaoLog();
	}

	private validarPermissaoLog() {
		const recursos = this.sessionServiceSdk.recursos;
		return (
			recursos !== undefined &&
			recursos.get(PerfilAcessoRecursoNome.LOG) !== undefined &&
			recursos.get(PerfilAcessoRecursoNome.LOG).consultar
		);
	}

	ngOnDestroy() {
		this.cobrancaService.definirTitleTreino("Sistema Pacto");
		this.subscriptionReloadDadosSituacoes.unsubscribe();
	}

	verAvisos() {
		const dialogRef = this.pactoModal.open(
			"Avisos",
			AvisosComponent,
			PactoModalSize.LARGE
		);
		dialogRef.componentInstance.cliente = String(
			this.dadosPessoais.codigoCliente
		);
		dialogRef.componentInstance.matricula = this.matricula;
		dialogRef.componentInstance.pessoa = this.dadosPessoais.codigoPessoa;
		dialogRef.componentInstance.update.subscribe((res) => {
			if (res === "update") {
				this.updateWarnings$.next();
				this.cd.detectChanges();
			} else if (res) {
				this.acaoClienteMensagem(res);
			}
		});
	}

	// Avisos
	getAvisos() {
		this.mensagensAvisoService
			.obterTodasMensagens(this.dadosPessoais.codigoPessoa)
			.subscribe((res: any) => {
				const avisosMedicos = res.content.filter(
					(aviso) => aviso.tipoMensagem === "AM"
				);
				const outrosAvisos = res.content.filter(
					(aviso) => aviso.tipoMensagem !== "AM"
				);
				this.avisos = [...avisosMedicos, ...outrosAvisos];
				this.getMessageAviso(this.avisos);
				this.processedAvisos = this.getMessageAviso(this.avisos.slice(0, 2));
				this.cd.detectChanges();
			});
	}

	getMessageAviso(avisos: any[]): string[] {
		if (!avisos || !Array.isArray(avisos)) {
			return [];
		}

		const prefixMap = {
			AM: "Aviso médico: ",
			AA: "Aviso ao consultor: ",
		};

		return avisos.map((aviso) => {
			const prefix = prefixMap[aviso.tipoMensagem];
			if (
				prefix &&
				!aviso.mensagem.toLowerCase().includes(prefix.toLowerCase())
			) {
				return `${prefix}${aviso.mensagemSemHTML}`;
			}
			return aviso.mensagemSemHTML;
		});
	}

	getButtonText(itemNumber: number): string {
		if (itemNumber > 2) {
			return `Adicionar / Ver todas (+${itemNumber})`;
		}

		if (itemNumber) {
			return `Adicionar / Ver todas`;
		}
		return `Adicionar`;
	}

	// Observação
	getObservacao() {
		this.admCoreObsService
			.findObservacaoTelaCliente1(
				this.dadosPessoais.codigoPessoa,
				this.dadosPessoais.matricula,
				{ page: 0, size: 2 }
			)
			.pipe(
				map((res: any) => {
					return res.content.map((data) => {
						return {
							...data,
							_dataCadastro: this.datePipe.transform(
								new Date(data.dataCadastro),
								"dd/MM/yyyy"
							),
						};
					});
				}),
				map((observacoes) => observacoes.slice(0, 2))
			)
			.subscribe((observacoes: any[]) => {
				this.observacaoList = observacoes;
				this.cd.detectChanges();
			});
	}

	alterarMatricula(): void {
		const openModalAlterarMatricula = () => {
			const dialogRef = this.pactoModal.open(
				"Alterar matrícula",
				AlterarMatriculaComponent,
				PactoModalSize.MEDIUM
			);
			dialogRef.componentInstance.dadosCliente = this.dadosPessoais;
			dialogRef.componentInstance.matricula = String(
				this.dadosPessoais.matricula
			);
			dialogRef.componentInstance.matriculaAlterada.subscribe((res) => {
				const currentUrl = this.router.url;
				const newUrl = currentUrl.replace(
					this.activatedRoute.snapshot.paramMap.get("aluno-matricula"),
					res
				);
				this.router.navigate([newUrl], {
					relativeTo: this.activatedRoute,
				});
			});
		};

		this.autorizarAcessoService
			.validarPermissaoUsuarioLogado(
				this.sessionService.chave,
				this.sessionService.loggedUser.usuarioZw,
				this.sessionService.empresaId,
				"AlterarMatricula",
				"2.59 - Alterar número de matrícula do cliente"
			)
			.subscribe(
				(response) => {
					// retorna o código do usuário caso tenha a permissao
					openModalAlterarMatricula();
				},
				(error) => {
					// dá erro caso não tenha
					const modalConfirmacao: any = this.matDialog.open(
						DialogAutorizacaoAcessoComponent,
						{
							disableClose: true,
							id: "autorizacao-acesso",
							autoFocus: false,
						}
					);
					modalConfirmacao.componentInstance.form
						.get("usuario")
						.patchValue(this.sessionService.loggedUser.username);

					modalConfirmacao.componentInstance.confirm.subscribe((result) => {
						this.autorizarAcessoService
							.validarPermissao(
								this.sessionService.chave,
								result.data.usuario,
								result.data.senha,
								"AlterarMatricula",
								"2.59 - Alterar número de matrícula do cliente",
								this.sessionService.empresaId.toString()
							)
							.subscribe(
								(response: any) => {
									openModalAlterarMatricula();
									modalConfirmacao.close(null);
								},
								({ error = {} }) => {
									const errorMsg =
										error.meta && error.meta.message
											? error.meta.message
											: `Usuário não possui permissão: 2.59 - Alterar número de matrícula do cliente.`;
									modalConfirmacao.close(null);
									this.notificationService.error(errorMsg);
								}
							);
					});
				}
			);
	}

	verificarAlteracoesParam(params: ParamMap): Observable<any> {
		this.loadInit();
		return new Observable<any>();
	}

	loadInit() {
		this.matricula = this.activatedRoute.snapshot.params["aluno-matricula"];
		this.marcarAlunoRecente();
		if (this.localStorageService.getLocalStorageItem("infoAlunoAberto")) {
			this.aberto =
				this.localStorageService.getLocalStorageItem("infoAlunoAberto") ===
				"true";
		}
		if (this.localStorageService.getLocalStorageItem("infoAlunoAberto")) {
			this.aberto =
				this.localStorageService.getLocalStorageItem("infoAlunoAberto") ===
				"true";
		}
		this.loadData();
	}

	fechar() {
		this.aberto = false;
		this.localStorageService.setLocalStorageItem("infoAlunoAberto", false);
	}

	abrir() {
		this.aberto = true;
		this.localStorageService.setLocalStorageItem("infoAlunoAberto", true);
	}

	abrirLinhaDoTempo() {
		const modal = this.pactoModal.open(
			"Linha do tempo",
			LinhaDoTempoComponent,
			PactoModalSize.SMALL,
			"modal-time-line"
		);
		const timeLine: LinhaDoTempoComponent = modal.componentInstance;
		timeLine.matricula = this.matricula;
		timeLine.pessoa = this.dadosPessoais.codigoPessoa;
	}

	loadData() {
		this.msAdmCoreService.dadosPessoais(this.matricula).subscribe((aluno) => {
			this.dadosPessoais = aluno;
			this.getAvisos();
			this.getObservacao();
			this.cobrancaService.definirTitleTreino(this.dadosPessoais.nome);
			if (this.dadosPessoais.dataMatricula) {
				this.dataMatricula = new Date(this.dadosPessoais.dataMatricula);
				this.dataMatricula.setHours(0);
				this.dataMatricula.setMinutes(0);
				this.dataMatricula.setSeconds(0);
				this.dataMatricula.setMilliseconds(0);

				const today = new Date();
				today.setHours(0);
				today.setMinutes(0);
				today.setSeconds(0);
				today.setMilliseconds(0);

				if (this.dataMatricula.getTime() === today.getTime()) {
					this.dataMatriculaHoje = true;
				}
			}

			sessionStorage.setItem(
				"codPessoa",
				JSON.stringify(this.dadosPessoais.codigoPessoa)
			);
			this.codigoPessoa = this.dadosPessoais.codigoPessoa;
			sessionStorage.setItem(
				"codCliente",
				JSON.stringify(this.dadosPessoais.codigoCliente)
			);

			this.cd.detectChanges();

			this.admLegadoTelaClienteService
				.dadosPagamentoCliente(
					this.sessionService.chave,
					String(this.dadosPessoais.codigoCliente)
				)
				.subscribe((res) => {
					this.dadosPagamentoCliente = res.content;
					this.cobrancaAutorizada =
						this.dadosPagamentoCliente.bloqueioCobrancaAutomaticaData === "";
					this.cd.detectChanges();
				});

			this.admLegadoTelaClienteService
				.convidadosInfo(
					this.sessionService.chave,
					this.dadosPessoais.codigoCliente
				)
				.pipe(
					map((resp) => {
						if (resp && resp.content) {
							return resp.content;
						}
						return resp;
					})
				)
				.subscribe((resp) => {
					this.infoConvidado = resp;
				});

			this.alunoService
				.obterInfoAlunoApp(
					null,
					this.dadosPessoais.codigoPessoa,
					this.dadosPessoais.codigoCliente
				)
				.subscribe(
					(infoAlunoApp) => {
						this.alunoAppInfo = infoAlunoApp;
						this.cd.detectChanges();
					},
					(httpErrorResponse) => {
						this.alunoAppInfo = undefined;
					}
				);

			this.initDadosImportacao();

			this.msAdmCoreService
				.usuarioBiometria(this.dadosPessoais.codigoPessoa)
				.subscribe(
					(assinatura) => {
						this.assinatura = assinatura;
						this.cd.detectChanges();
					},
					(httpErrorResponse) => {
						this.assinatura = undefined;
					}
				);

			this.loadIntegracaoMqv();
			this.loadIntegracaoMgb();

			this.loadClienteRestricoes();

			this.mensagensAvisoService
				.obterClienteMensagem(this.dadosPessoais.codigoPessoa)
				.subscribe((res) => {
					if (res && res.content) {
						if (res.content.codigo && res.content.codigo > 0) {
							this.avisoAoConsultor(false);
						}
					}
				});

			this.admLegadoTelaClienteService
				.apresentarMenuContrato(
					this.sessionService.chave,
					Number(this.sessionService.empresaId),
					this.dadosPessoais.codigoCliente,
					{}
				)
				.subscribe((data) => {
					this.apresentarMenuContrato = data.content;
					this.cd.detectChanges();
				});
			this.cd.detectChanges();

			this.admLegadoTelaClienteService
				.apresentarAtivarAcessoConvidado(
					this.sessionService.chave,
					this.dadosPessoais.codigoCliente,
					this.sessionService.loggedUser.usuarioZw
				)
				.subscribe((resp) => {
					this.ativarAcessoConvidado = resp.content;
				});
			this.possuiPermissaoVerAbaContrato();
			this.loadPermissaoApresentarMenuContrato();
		});

		this.msAdmCoreService
			.dadosAuxiliares(this.matricula)
			.subscribe((dadosAu) => {
				this.dadosAuxiliares = dadosAu;
				this.cd.detectChanges();
			});
		this.msAdmCoreService.dadosPlano(this.matricula).subscribe((plano) => {
			this.dadosPlano = plano;
			this.preencherDataVinculoAtual();
			this.cd.detectChanges();
		});
		this.msAdmCoreService.totalPontos(this.matricula).subscribe((pontos) => {
			this.pontos = pontos;
			this.cd.detectChanges();
		});
		this.msAdmCoreService
			.saldoContaCorrente(this.matricula)
			.subscribe((saldo) => {
				this.saldo = saldo;
				this.saldoClasse =
					this.saldo > 0
						? "saldo-positivo"
						: this.saldo < 0
						? "saldo-negativo"
						: "saldo-zero";
				this.cd.detectChanges();

				if (this.saldo < 0) {
					this.botaoTransferenciaSaldo = "Receber Debito";
				} else if (this.saldo === 0) {
					this.botaoTransferenciaSaldo = "";
				} else {
					this.botaoTransferenciaSaldo = "Transferir saldo";
				}

				if (this.saldo < 0) {
					this.botaoAjustarSaldo = "Ajustar Debito";
				} else if (this.saldo === 0) {
					this.botaoAjustarSaldo = "";
				} else {
					this.botaoAjustarSaldo = "Ajustar saldo";
				}
			});

		this.alunoService
			.obterAlunoMatricula(this.matricula)
			.pipe(
				switchMap((aluno: any) => {
					return this.alunoService.obterAluno(aluno.id);
				})
			)
			.subscribe((resp) => {
				this.aluno = resp;
				this.cd.detectChanges();
				if (!this.permiteVisualizarClienteTreino()) {
					this.listaPessoas();
					return;
				}
			});

		this.modalidades = [];

		this.admContratoService
			.getContratosByMatricula(this.matricula, {
				filters: JSON.stringify({}),
				configs: JSON.stringify({}),
				// page: 0,
				// size: 10,
			})
			.pipe(
				map((resp: any) => {
					if (!resp || !resp.content) {
						return [];
					}

					return resp.content.filter((contrato) => {
						return contrato.situacao === "AT";
					});
				}),
				switchMap((contratos: any) => {
					this.planosAtivos = contratos;

					if (contratos.lenght === 0) {
						return of([]);
					}

					const modalidades: Observable<any>[] = contratos.map(
						(contrato: any): Observable<any> => {
							return this.admContratoService
								.modalidadesContrato(contrato.codigo)
								.pipe(
									map((resp) => {
										return resp ? resp.content : null;
									})
								);
						}
					);

					return combineLatest(modalidades);
				})
			)
			.subscribe((resp) => {
				resp.forEach((item) => {
					item.forEach((contrato) => {
						if (contrato && contrato.modalidade) {
							this.modalidades.push(contrato.modalidade);
							this.cd.detectChanges();
						}
					});
				});
			});

		// this.msPactopayApiCobranca
		// .obterInformacoesPessoa(this.matricula)
		// .subscribe(aluno => {
		//     this.cobrancaAutorizada =
		//         aluno.data_bloqueio_cobranca_automatica === '';
		//     this.cd.detectChanges();
		// });

		this.cd.detectChanges();
	}

	loadDadosPessoais() {
		this.msAdmCoreService.dadosPessoais(this.matricula).subscribe((aluno) => {
			this.dadosPessoais = aluno;
			this.cd.detectChanges();
		});
	}

	loadDadosPlano() {
		this.msAdmCoreService.dadosPlano(this.matricula).subscribe((plano) => {
			this.dadosPlano = plano;
			this.cd.detectChanges();
		});
	}

	initDadosImportacao() {
		this.msAdmCoreService
			.usuarioVerificado(this.dadosPessoais.codigoPessoa)
			.subscribe(
				(usuarioVerificacao) => {
					this.usuarioVerificacao = usuarioVerificacao;
					this.cd.detectChanges();
				},
				(httpErrorResponse) => {
					this.usuarioVerificacao = undefined;
				}
			);
	}

	abreviarNome(nome): string {
		try {
			return nome.split(" ").slice(0, 2).join(" ");
		} catch (e) {
			return nome;
		}
	}

	preencherDataVinculoAtual() {
		if (this.dadosPlano && this.dadosPlano.inicioVinculoAtual) {
			this.dataVinculoAtual = new Date(this.dadosPlano.inicioVinculoAtual);
			this.dataVinculoAtual.setHours(0);
			this.dataVinculoAtual.setMinutes(0);
			this.dataVinculoAtual.setSeconds(0);
			this.dataVinculoAtual.setMilliseconds(0);
			this.processarCAC();
		}
	}

	processarCAC() {
		this.admLegadoTelaClienteService
			.cacCliente(
				this.sessionService.chave,
				parseInt(this.sessionService.empresaId)
			)
			.subscribe(
				(resp) => {
					if (resp) {
						this.cacCliente = resp.content;
					}
					this.cd.detectChanges();
				},
				(httpErrorResponse) => {
					console.log(httpErrorResponse);
				}
			);
	}

	marcarAlunoRecente() {
		this.alunoFavoritoRecenteService
			.marcarCliente(Number(this.matricula), "RE")
			.subscribe((response) => {});
	}

	stringToKebab(str: string) {
		if (str) {
			return str
				.match(
					/[A-Z]{2,}(?=[A-Z][a-z]+[0-9]*|\b)|[A-Z]?[a-z]+[0-9]*|[A-Z]|[0-9]+/g
				)
				.map((x) => x.toLowerCase())
				.join("-");
		} else {
			return "";
		}
	}

	goToVendasAcessos() {
		this.isSubMenuVendasExpandido = !this.isSubMenuVendasExpandido;
	}

	goToIntegracoes() {
		this.isSubMenuIntegracoesExpandido = !this.isSubMenuIntegracoesExpandido;
	}

	goToGympass() {
		this.router.navigate(["pessoas", "perfil-v2", this.matricula, "gympass"]);
	}

	goToGogood() {
		this.router.navigate(["pessoas", "perfil-v2", this.matricula, "gogood"]);
	}

	goTotalpass() {
		this.router.navigate(["pessoas", "perfil-v2", this.matricula, "totalpass"]);
	}

	goHistoricoDeIndicacoes() {
		this.router.navigate([
			"pessoas",
			"perfil-v2",
			this.matricula,
			"historico-indicacoes",
		]);
	}

	goDadosPessoais(): void {
		if (this.permiteEditarCliente()) {
			this.router.navigate([
				"/pessoas",
				"perfil-v2",
				"configuracoes-cliente",
				this.matricula,
				this.dadosPessoais.codigoPessoa,
			]);
		} else {
			this.notificationService.warning(
				"Usuario não possui as permissões necessárias (2.04 - Cliente) para acessar tela de edição."
			);
		}
	}

	goVinculos(): void {
		if (!this.permiteEditarVinculo()) {
			this.notificationService.error(
				'Você não possui a permissão "2.29 - Vínculos de cliente e de colaborador"',
				{
					timeout: 5000,
					bodyMaxLength: 300,
				}
			);
			return;
		}
		this.router.navigate(
			[
				"pessoas",
				"perfil-v2",
				"configuracoes-cliente",
				this.matricula,
				this.dadosPessoais.codigoPessoa,
			],
			{ queryParams: { aba: "VINCULOS" } }
		);
	}

	goPactoPay() {
		this.router.navigate(["pessoas", "perfil-v2", this.matricula, "pactopay"]);
	}

	goCRM() {
		this.router.navigate(["pessoas", "perfil-v2", this.matricula, "crm"]);
	}

	definirSenhaDeAcesso() {
		this.autorizarAcessoService
			.validarPermissaoUsuarioLogado(
				this.sessionService.chave,
				this.sessionService.loggedUser.usuarioZw,
				this.sessionService.empresaId,
				"AdicionarAlterarSenhaAcesso",
				"3.34 - Adicionar/Alterar Senha Acesso - Autorizar"
			)
			.subscribe(
				(response) => {
					const dialogRef = this.pactoModal.open(
						"Definir senha de acesso",
						ModalDefinirSenhaAcessoComponent,
						PactoModalSize.LARGE
					);
					dialogRef.componentInstance.dadosPessoais = this.dadosPessoais;
				},
				(httpResponseError) => {
					this.notificationService.error(httpResponseError.error.meta.message);
				}
			);
	}

	bloqueioDeAcesso() {
		const dialogRef = this.pactoModal.open(
			"Aviso / Bloqueio de catraca",
			ModalBloqueioAcessoCatracaComponent,
			PactoModalSize.LARGE
		);
		dialogRef.componentInstance.dadosPessoais = this.dadosPessoais;
	}

	registrarAcessoManual() {
		// this.autorizarAcessoService
		//     .validarPermissaoUsuarioLogado(
		//         this.sessionService.chave,
		//         this.sessionService.loggedUser.usuarioZw,
		//         this.sessionService.empresaId,
		//         'PermiteRegistrarAcessoAvulso',
		//         '9.38 - Permite registrar acesso manual'
		//     )
		//     .subscribe(
		// response => {
		const dialogRef = this.pactoModal.open(
			"Registrar acesso manual",
			ModalRegistrarAcessoManualComponent,
			PactoModalSize.MEDIUM
		);
		dialogRef.componentInstance.dadosPessoais = this.dadosPessoais;
		// },
		// httpResponseError => {
		//     this.notificationService.error(httpResponseError.error.meta.message);
		// }
		// );
	}

	goToVendasCompartilharLink() {
		this.isSubMenuCompartilharLinkExpandido =
			!this.isSubMenuCompartilharLinkExpandido;
	}

	goToContaCorrente() {
		this.isSubMenuContaCorrenteExpandido =
			!this.isSubMenuContaCorrenteExpandido;
	}

	goToClubeVantagens() {
		this.isSubMenuClubeVantagensExpandido =
			!this.isSubMenuClubeVantagensExpandido;
	}

	linkDeCadastroDeCartao() {
		const dialogRef = this.pactoModal.open(
			"Compartilhar link cadastro de cartão",
			ModalShareLinkClienteComponent,
			PactoModalSize.MEDIUM
		);
		dialogRef.componentInstance.codCliente = this.dadosPessoais.codigoCliente;
		dialogRef.componentInstance.typeLink = "cadastrar";
		if (
			this.dadosPessoais.telefones &&
			Array.isArray(this.dadosPessoais.telefones) &&
			this.dadosPessoais.telefones[0]
		) {
			dialogRef.componentInstance.telefoneCliente =
				this.dadosPessoais.telefones[0].numero;
		}
	}

	linkDePagamentoOld(
		todasEmAberto,
		parcelasSelecionadas,
		numeroVezesParcelamentoOperadora
	) {
		const dialogRef = this.pactoModal.open(
			"Compartilhar link de pagamento",
			ModalShareLinkClienteComponent,
			PactoModalSize.MEDIUM
		);

		dialogRef.componentInstance.codCliente = this.dadosPessoais.codigoCliente;
		dialogRef.componentInstance.typeLink = "pagamento";
		dialogRef.componentInstance.todasEmAberto = todasEmAberto;
		dialogRef.componentInstance.parcelasSelecionadas = parcelasSelecionadas;
		dialogRef.componentInstance.numeroVezesParcelamentoOperadora =
			numeroVezesParcelamentoOperadora;

		if (
			this.dadosPessoais.telefones &&
			Array.isArray(this.dadosPessoais.telefones) &&
			this.dadosPessoais.telefones[0]
		) {
			dialogRef.componentInstance.telefoneCliente =
				this.dadosPessoais.telefones[0].numero;
		}
	}

	linkDePagamento() {
		const classSizeModal =
			window.innerWidth < 580
				? "modal-incluir-aluno-avaliacao-progresso modal-sm"
				: "modal-mxl";
		const modalRef = this.modal.open(IncluirParcelasLinkModalComponent, {
			centered: true,
			windowClass: classSizeModal,
			backdrop: "static",
		});
		const modal: IncluirParcelasLinkModalComponent = modalRef.componentInstance;
		modal.chave = this.sessionService.chave;
		modal.pessoa = this.dadosPessoais.codigoPessoa;

		modalRef.result.then(
			(retorno) => {
				if (retorno && retorno.tipoSelecionado === "1") {
					this.linkDePagamentoOld(null, null, 1);
				} else if (retorno && retorno.tipoSelecionado === "2") {
					this.linkDePagamentoOld(true, null, 1);
				} else if (
					retorno &&
					retorno.tipoSelecionado === "3" &&
					retorno.parcelasSelecionadas
				) {
					let parcelas = "";
					retorno.parcelasSelecionadas.forEach((element) => {
						if (parcelas.length > 0) {
							parcelas = parcelas + ";" + element;
						} else {
							parcelas = element + "";
						}
					});
					this.linkDePagamentoOld(
						null,
						parcelas,
						retorno.parcelamentoOperadoraSelecionado
					);
				}
			},
			() => {}
		);
	}

	goToVendasLancarAviso() {
		this.isSubMenuLancarAvisoExpandido = !this.isSubMenuLancarAvisoExpandido;
	}

	expandirSubmenuCarteirinha() {
		this.isSubMenuCarteirinhaExpandido = !this.isSubMenuCarteirinhaExpandido;
	}

	solicitarCarteirinha() {
		const modalRef = this.pactoModal.open(
			"Solicitar Carteirinha",
			ModalCadastroStatusComponent,
			PactoModalSize.LARGE
		);
		modalRef.componentInstance.bodyText =
			"Atenção: ao confirmar a solicitação de uma nova carteirinha implicará na substituição da carteirinha atual.";
		modalRef.componentInstance.labelCancel = "Cancelar";
		modalRef.componentInstance.labelCadastro = "Confirmar solicitação";
		modalRef.componentInstance.result.subscribe((data) => {
			if (data.status) {
				const dadosConsulta = {
					tipoOperacao: "SOLICITAR_NOVA_CARTEIRINHA",
					codigoCliente: `${this.dadosPessoais.codigoCliente}`,
					codigoEmpresa: this.sessionService.empresaId,
					chave: this.sessionService.chave,
				};
				this.admCoreApiNegociacaoService
					.chamarServletCarteirinhaCliente(dadosConsulta)
					.subscribe(
						(resultado) => {
							if (resultado.error) {
								this.notificationService.error(resultado.error);
								return;
							}
							this.notificationService.success(
								"Solicitação de carteirinha criada com sucesso!"
							);
							this.pactoPrintService
								.verificarSaude()
								.subscribe((response: any | undefined) => {
									if (response && response.isAlive) {
										this.visualizarCarteirinha();
									}
								});
						},
						(e) => {
							console.log("ERRO_SOLICITAR_CARTEIRINHA", e);
							if (e.error.meta && e.error.meta.message) {
								this.notificationService.error(e.error.meta.message);
							} else {
								this.notificationService.error(
									"Não foi possível solicitar a carteirinha."
								);
							}
						}
					);
				modalRef.close("executado");
			} else {
				modalRef.close("cancelado");
			}
		});
	}

	visualizarCarteirinha() {
		if (this.isProcessingCarteirinha) {
			return;
		}

		this.isProcessingCarteirinha = true;

		const dadosConsulta = {
			tipoOperacao: "CONSULTAR_CARTEIRINHA_CLIENTE",
			codigoCliente: `${this.dadosPessoais.codigoCliente}`,
			codigoEmpresa: this.sessionService.empresaId,
			chave: this.sessionService.chave,
		};

		this.admCoreApiNegociacaoService
			.chamarServletCarteirinhaCliente(dadosConsulta)
			.subscribe((dadosCarteirinha) => {
				if (dadosCarteirinha && dadosCarteirinha.base64) {
					try {
						this.exibirCarteirinhaNovaAba(dadosCarteirinha.base64);
					} catch (error) {
						console.log("ERRO_VISUALIZAR_CARTEIRINHA", error);
						this.notificationService.error("Erro ao visualizar a carteirinha.");
					}
					this.isProcessingCarteirinha = false;
				} else {
					let base64Data;
					this.pactoPrintService
						.verificarSaude()
						.pipe(
							concatMap((response: any) => {
								if (response && response.isAlive) {
									return this.pactoPrintService.gerarCarteirinhaBase64(
										dadosCarteirinha,
										this.dadosPessoais.situacao
									);
								} else {
									throw new Error(
										"A carteirinha ainda não foi gerada. Para gerá-la, é necessário que o serviço Pacto Print esteja em execução."
									);
								}
							}),
							concatMap((response: any) => {
								if (response.erro) {
									throw new Error(response.erro);
								}
								base64Data = response.base64;
								const body = {
									...dadosConsulta,
									tipoOperacao: "SALVAR_CARTEIRINHA_BASE64",
									codigoCarteirinha: dadosCarteirinha.codigo,
									base64: base64Data,
								};
								return this.admCoreApiNegociacaoService.chamarServletCarteirinhaCliente(
									body
								);
							}),
							concatMap((response: any) => {
								if (response.error) {
									throw new Error(
										`Erro ao salvar base64 da carteirinha. Erro: ${response.error}`
									);
								} else {
									this.exibirCarteirinhaNovaAba(base64Data);
									return of({ sucesso: true });
								}
							}),
							catchError((error) => {
								return of({ sucesso: false, erro: error });
							})
						)
						.subscribe((resultado: any) => {
							if (resultado.sucesso) {
								console.log("SUCESSO BASE64", resultado);
								this.isProcessingCarteirinha = false;
							} else {
								console.log("ERRO_VISUALIZAR_CARTEIRINHA", resultado.erro);
								this.notificationService.error(resultado.erro);
							}
							this.isProcessingCarteirinha = false;
						});
				}
			});
	}

	avisoMedico() {
		const openModalAvisoMedico = () => {
			this.alunoService
				.obterClienteMensagemMatricula(this.matricula, "AM")
				.subscribe((result) => {
					const modal: PactoModalRef = this.pactoModal.open(
						"Aviso médico",
						ModalAvisoAlunoComponent,
						PactoModalSize.MEDIUM
					);
					const aviso = result.toString().replace(/&nbsp;/g, " ");
					modal.componentInstance.matricula = this.matricula;
					modal.componentInstance.aviso.setValue(aviso);
					modal.result.then(
						(resultModal) => {
							this.state.update$.next(true);
							this.dadosPlano.avisos = resultModal === "delete" ? 0 : 1;
							this.cd.detectChanges();
						},
						() => {}
					);

					modal.componentInstance.update.subscribe(() => {
						this.updateWarnings$.next();
					});
				});
		};
		this.autorizarAcessoService
			.validarPermissaoUsuarioLogado(
				this.sessionService.chave,
				this.sessionService.loggedUser.usuarioZw,
				this.sessionService.empresaId,
				"LancarMensagemMedico",
				"2.33 - Lançar aviso médico a partir da tela de informações de cliente"
			)
			.subscribe(
				(response) => {
					// retorna o código do usuário caso tenha a permissao
					openModalAvisoMedico();
				},
				(error) => {
					const errorMsg =
						error.meta && error.meta.message
							? error.meta.message
							: `Usuário não possui permissão: 2.33 - Lançar aviso médico a partir da tela de informações de cliente.`;
					this.notificationService.error(errorMsg);
				}
			);
	}

	avisoAoConsultor(exibirMensagemPermissao: boolean) {
		if (!this.permissaoAvisos) {
			if (exibirMensagemPermissao) {
				const errorMsg = "Usuário não possui permissão: 13.14 - Ver avisos.";
				this.notificationService.error(errorMsg);
			}
			return;
		}
		const openModalAvisoConsultor = () => {
			const dialogRef = this.pactoModal.open(
				"Aviso ao consultor",
				ModalAvisoConsultorComponent,
				PactoModalSize.MEDIUM
			);

			dialogRef.componentInstance.matricula = this.matricula;
			dialogRef.componentInstance.codCliente = this.dadosPessoais.codigoCliente;
			dialogRef.componentInstance.codPessoa = this.dadosPessoais.codigoPessoa;

			dialogRef.componentInstance.update.subscribe(() => {
				this.updateWarnings$.next();
			});
		};
		openModalAvisoConsultor();
	}

	goToVendasRelacionamento() {
		this.isSubMenuRelacionamentoExpandido =
			!this.isSubMenuRelacionamentoExpandido;
	}

	abrirBoletinsDeVisita() {
		this.clientDiscoveryService
			.linkZw(this.sessionService.usuarioOamd, this.sessionService.empresaId)
			.subscribe((urlZw) => {
				let url = `${urlZw}&funcionalidadeNome=BOLETIM_VISITA_CLIENTE&jspPage=questionarioClienteForm.jsp&codPessoa=${this.dadosPessoais.codigoPessoa}`;
				url += `&matriculaCliente=${this.matricula}&prepararTelaBoletimVisita=true&menu=true&origem=angular`;
				this.abrirPopup(url, "Questionario", 800, 595);
			});
	}

	objetivoDoAluno() {
		const dialogRef = this.pactoModal.open(
			"Objetivo do aluno",
			ModalObjetivoAlunoComponent,
			PactoModalSize.MEDIUM
		);
	}

	orcamento() {
		this.clientDiscoveryService
			.linkZw(this.sessionService.usuarioOamd, this.sessionService.empresaId)
			.subscribe((result) => {
				const url = `${result}&codCliente=${this.dadosPessoais.codigoCliente}&operacaoClienteEnumName=REALIZAR_ORCAMENTO&isOperacaoFinanceiro=true`;
				window.open(url, "_self");
			});
	}

	goToVendasArmario() {
		this.admLegadoTelaClienteService
			.configuracoesSistema(this.sessionService.chave, 0)
			.subscribe((data) => {
				const modalRef = this.pactoModal.open(
					"Armario",
					ModalArmarioComponent,
					PactoModalSize.LARGE,
					"armario-modal"
				);
				modalRef.componentInstance.alunoData = this.dadosPessoais;
				modalRef.componentInstance.configuracoesSistemaData = data;
			});
	}

	abrirHistoricoCarteirinha() {
		const modalRef = this.pactoModal.open(
			"Historico de Carteirinha",
			ModalHistoricoCarteiraComponent,
			PactoModalSize.LARGE,
			"armario-modal"
		);
		const dadosConsulta = {
			tipoOperacao: "CONSULTAR_HISTORICO",
			codigoCliente: `${this.dadosPessoais.codigoCliente}`,
			codigoEmpresa: this.sessionService.empresaId,
			chave: this.sessionService.chave,
		};
		this.admCoreApiNegociacaoService
			.chamarServletCarteirinhaCliente(dadosConsulta)
			.subscribe((response) => {
				console.log(response);
				modalRef.componentInstance.carteirinhaData = response.historico;
				modalRef.componentInstance.cd.detectChanges();
			});
	}

	goToVendasConvidado() {
		const modal: PactoModalRef = this.pactoModal.open(
			"Adicionar convidado",
			ModalConvidadosComponent,
			PactoModalSize.MEDIUM,
			"convidado-modal"
		);
		modal.componentInstance.matricula = this.matricula;
		modal.componentInstance.cliente = this.dadosPessoais.codigoCliente;
		modal.componentInstance.clienteEmpresa = this.dadosPessoais.empresa.codigo;
		modal.componentInstance.chave = this.sessionService.chave;
		modal.componentInstance.usuarioLogado =
			this.sessionService.loggedUser.usuarioZw;
		modal.componentInstance.permissaoConsultarAlunosCaixaAbertoTodasEmpresas =
			this.permissaoService.temPermissaoAdm("9.50");
	}

	goToVendasGympass() {}

	goToMaisDiaria() {
		this.autorizarAcessoService
			.validarPermissaoUsuarioLogado(
				this.sessionService.chave,
				this.sessionService.loggedUser.usuarioZw,
				this.sessionService.empresaId,
				"AulaAvulsaDiaria",
				"4.01 - Diária"
			)
			.subscribe(
				(response) => {
					this.router.navigateByUrl(`/adm/diarias/` + this.matricula);
				},
				(httpResponseError) => {
					this.notificationService.error(httpResponseError.error.meta.message);
				}
			);
	}

	goToMaisVendaAvulsa() {
		this.autorizarAcessoService
			.validarPermissaoUsuarioLogado(
				this.sessionService.chave,
				this.sessionService.loggedUser.usuarioZw,
				this.sessionService.empresaId,
				"VendaAvulsa",
				"4.11 - Vendas avulsas"
			)
			.subscribe(
				(response) => {
					this.admCoreApiNegociacaoService
						.recursoHabilitado("VENDA_AVULSA")
						.subscribe({
							next: (responseV) => {
								if (responseV) {
									this.acessarVendaAvulsaNova();
								} else {
									this.acessarVendaAvulsaAntiga();
								}
							},
							error: (err) => {
								this.acessarVendaAvulsaNova();
							},
						});
				},
				(httpResponseError) => {
					this.notificationService.error(httpResponseError.error.meta.message);
				}
			);
	}

	acessarVendaAvulsaNova() {
		this.router.navigateByUrl(`/adm/venda-avulsa/` + this.matricula);
	}

	acessarVendaAvulsaAntiga() {
		this.clientDiscoveryService
			.linkZw(this.sessionService.usuarioOamd, this.sessionService.empresaId)
			.subscribe((urlZw) => {
				let url = `${urlZw}&funcionalidadeNome=VENDA_AVULSA&jspPage=vendaAvulsaForm.jsp&codPessoa=${this.dadosPessoais.codigoPessoa}`;
				url += `&matriculaCliente=${this.matricula}&prepararVendaAvulsa=true&menu=true&origem=angular`;
				window.open(url, "_self");
			});
	}

	abrirCaixaEmAberto() {
		this.autorizarAcessoService
			.validarPermissaoUsuarioLogado(
				this.sessionService.chave,
				this.sessionService.loggedUser.usuarioZw,
				this.sessionService.empresaId,
				"CaixaEmAberto",
				"2.44 - Visualizar ou operar o Caixa em aberto"
			)
			.subscribe(
				(response) => {
					this.admCoreApiNegociacaoService
						.recursoHabilitado("CAIXA_ABERTO")
						.subscribe({
							next: (responseV) => {
								if (responseV) {
									this.acessarCaixaEmAbertoNovo();
								} else {
									this.acessarCaixaEmAbertoLegado();
								}
							},
							error: (err) => {
								this.acessarCaixaEmAbertoLegado();
							},
						});
				},
				(httpResponseError) => {
					this.notificationService.error(httpResponseError.error.meta.message);
				}
			);
	}

	acessarCaixaEmAbertoNovo() {
		this.router.navigateByUrl(
			"/adm/caixa-em-aberto/lista/" + this.dadosPessoais.codigoPessoa
		);
	}

	acessarCaixaEmAbertoLegado() {
		this.clientDiscoveryService
			.linkZw(this.sessionService.usuarioOamd, this.sessionService.empresaId)
			.subscribe((urlZw) => {
				let url = `${urlZw}&funcionalidadeNome=CAIXA_EM_ABERTO&jspPage=tela8.jsp&codPessoa=${this.dadosPessoais.codigoPessoa}`;
				url += `&matriculaCliente=${this.matricula}`;
				url += `&nomePessoa=${this.dadosPessoais.nome}&prepararCaixaEmAberto=true&menu=true&origem=angular`;
				window.open(url, "_self");
			});
	}

	abrirBoletimVisita(clienteMensagem) {
		this.clientDiscoveryService
			.linkZw(this.sessionService.usuarioOamd, this.sessionService.empresaId)
			.subscribe((urlZw) => {
				let url = `${urlZw}&funcionalidadeNome=BOLETIM_VISITA_CLIENTE&jspPage=questionarioClienteForm.jsp&codPessoa=${this.dadosPessoais.codigoPessoa}`;
				url += `&matriculaCliente=${this.matricula}&prepararTelaBoletimVisita=true&menu=true&codigoAviso=${clienteMensagem.codigo}&origem=angular`;

				this.abrirPopup(url, "MovimentoContaCorrenteCliente", 800, 595);
			});
	}

	goTomovimentacaoFinanceira(): void {
		const checkInterval = setInterval(() => {
			const codigoPessoa = this.dadosPessoais.codigoPessoa;
			const matricula = this.matricula;

			if (codigoPessoa && matricula) {
				clearInterval(checkInterval);

				this.clientDiscoveryService
					.linkZw(
						this.sessionService.usuarioOamd,
						this.sessionService.empresaId
					)
					.subscribe((urlZw) => {
						let url = `${urlZw}&funcionalidadeNome=MOVIMENTO_CC_CLIENTE&jspPage=movimentoContaCorrenteClienteCons.jsp&codPessoa=${codigoPessoa}`;
						url += `&matriculaCliente=${matricula}&prepararTelaMovimentoFinanceiro=true&menu=true&origem=angular`;

						this.abrirPopup(url, "MovimentoContaCorrenteCliente", 800, 595);
					});
			}
		}, 300);
	}

	goToHistoricoPontuacao() {
		this.clientDiscoveryService
			.linkZw(this.sessionService.usuarioOamd, this.sessionService.empresaId)
			.subscribe((urlZw) => {
				let url = `${urlZw}&funcionalidadeNome=RELATORIO_PONTUACAO&jspPage=historicoPontosResumo.jsp&codPessoa=${this.dadosPessoais.codigoPessoa}`;
				url += `&matriculaCliente=${this.matricula}&prepararTelaPontuacao=true&menu=true&origem=angular`;
				this.abrirPopup(url, "Historico Pontos Por Aluno", 800, 595);
			});
	}

	abrirPopup(
		URL: string,
		nomeJanela: string,
		comprimento: number,
		altura: number
	): boolean {
		const posTopo = 0;
		const posEsquerda = 0;
		const atributos = `left=${posEsquerda}, screenX=${posEsquerda}, top=${posTopo}, screenY=${posTopo}, width=${comprimento}, height=${altura}, dependent=yes, menubar=no, toolbar=no, resizable=yes, scrollbars=yes`;

		const parameterCaracter = URL.includes("?") ? "&" : "?";
		const urlPopup = URL + parameterCaracter + "from=popup";
		const win = window.open(urlPopup, nomeJanela, atributos);
		const timer = setInterval(() => {
			if (win.closed) {
				clearInterval(timer);
				this.loadData();
			}
		}, 500);
		return false;
	}

	getItemsArray(array: any[]) {
		if (!array || !Array.isArray(array)) {
			return [];
		}
		return array.slice(1, array.length);
	}

	getObservationsButtonText(obsNumber): string {
		if (obsNumber) {
			return `Observações (+${obsNumber})`;
		}

		return `Observações`;
	}

	getAvisosButtonText(avisosNumber): string | null {
		if (avisosNumber) {
			return `Ver mais (+${avisosNumber})`;
		}

		return null;
	}

	openObservations() {
		if (!this.permiteVisualizarObservacao()) {
			this.notificationService.error(
				'Você não possui a permissão "2.36 - Lançar observação para o cliente"',
				{
					timeout: 5000,
					bodyMaxLength: 300,
				}
			);
			return;
		}

		const dialogRef = this.pactoModal.open(
			"Observações",
			ObservacoesComponent,
			PactoModalSize.LARGE,
			"design-system3-adjust"
		);
		dialogRef.componentInstance.cliente = String(
			this.dadosPessoais.codigoCliente
		);
		dialogRef.componentInstance.pessoa = String(
			this.dadosPessoais.codigoPessoa
		);
		dialogRef.componentInstance.matricula = String(
			this.dadosPessoais.matricula
		);
		dialogRef.componentInstance.usuario = String(
			this.sessionService.codigoUsuarioZw
		);
		dialogRef.componentInstance.observacoesV1 = this.observacoesV1;

		dialogRef.result.then(() => {
			this.msAdmCoreService.dadosPlano(this.matricula).subscribe((plano) => {
				this.dadosPlano = plano;
				this.cd.detectChanges();
			});
		});
		dialogRef.componentInstance.update.subscribe(() => {
			this.getObservacao();
			this.updateObservations$.next();
		});
	}

	permiteVisualizarObservacao(): any {
		const permition = this.permissaoCliente2_36;
		const isPermited =
			permition &&
			permition.tipoPermissoes.find(
				(tp) =>
					tp === PerfilRecursoPermissoTipo.CONSULTAR ||
					tp === PerfilRecursoPermissoTipo.INCLUIR ||
					tp === PerfilRecursoPermissoTipo.EDITAR ||
					tp === PerfilRecursoPermissoTipo.EXCLUIR ||
					tp === PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR ||
					tp === PerfilRecursoPermissoTipo.TOTAL ||
					tp === PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR
			);
		const retor = isPermited !== undefined && isPermited;
		this.canObservation = retor ? true : false;
		return retor;
	}
	openLogAtividades() {
		if (this.permissaoLog) {
			this.router.navigate([
				"pessoas",
				"perfil-v2",
				this.matricula,
				"log-atividade",
				this.dadosPessoais.codigoPessoa,
			]);
		} else {
			this.notificationService.warning(
				this.traducoes.getLabel("mensagem-sem-permissao")
			);
		}
	}

	ordenarByAvisoMedico(avisos: any[]): any[] {
		const avisosMedicos = avisos.filter((aviso) => aviso.tipoMensagem === "AM");
		const outrosAvisos = avisos.filter((aviso) => aviso.tipoMensagem !== "AM");

		return avisosMedicos.concat(outrosAvisos);
	}

	debitoContaCorrente() {
		this.clientDiscoveryService
			.linkZw(this.sessionService.usuarioOamd, this.sessionService.empresaId)
			.subscribe((urlZw) => {
				let url = `${urlZw}&funcionalidadeNome=RECEBER_DEBITO_CONTA_CORRENTE&jspPage=gerarProdutoContaCorrenteForm.jsp&codPessoa=${this.dadosPessoais.codigoPessoa}`;
				url += `&matriculaCliente=${this.matricula}&prepararTelaDeDebitoContaCorrente=true&menu=true&origem=angular`;
				this.abrirPopup(url, "Receber debito conta corrente", 800, 595);
			});
	}

	transferenciaDeSaldo() {
		this.clientDiscoveryService
			.linkZw(this.sessionService.usuarioOamd, this.sessionService.empresaId)
			.subscribe((urlZw) => {
				let url = `${urlZw}&funcionalidadeNome=TRANSFERENCIA_SALDO_CONTA_CORRENTE&jspPage=transferenciaContaClienteForm.jsp&codPessoa=${this.dadosPessoais.codigoPessoa}`;
				url += `&matriculaCliente=${this.matricula}&prepararTelaDeTransferenciaDeSaldo=true&menu=true&origem=angular`;
				this.abrirPopup(url, "Transferência saldo conta corrente", 800, 595);
			});
	}

	contaCorrenteAjusteSaldo() {
		this.clientDiscoveryService
			.linkZw(this.sessionService.usuarioOamd, this.sessionService.empresaId)
			.subscribe((urlZw) => {
				let url = `${urlZw}&funcionalidadeNome=AJUSTE_SALDO_CONTA_CORRENTE&jspPage=ajustesSaldoContaCorrente.jsp&codPessoa=${this.dadosPessoais.codigoPessoa}`;
				url += `&matriculaCliente=${this.matricula}&prepararTelaDeAjusteDeSaldo=true&menu=true&origem=angular`;
				this.abrirPopup(url, "Ajuste de saldo conta corrente", 800, 595);
			});
	}

	goToClubeVantagensPontuacaoCliente() {
		this.clientDiscoveryService
			.linkZw(this.sessionService.usuarioOamd, this.sessionService.empresaId)
			.subscribe((urlZw) => {
				let url = `${urlZw}&funcionalidadeNome=AJUSTE_PONTUACAO_CONTA_CORRENTE&jspPage=ajustePontosCliente.jsp&codPessoa=${this.dadosPessoais.codigoPessoa}`;
				url += `&matriculaCliente=${this.matricula}&prepararTelaAjusteDePontuacao=true&menu=true&origem=angular`;
				this.abrirPopup(url, "Ajuste de pontuação conta corrente", 800, 595);
			});
	}

	goToClubeVantagensLancamentoBrinde() {
		this.clientDiscoveryService
			.linkZw(this.sessionService.usuarioOamd, this.sessionService.empresaId)
			.subscribe((urlZw) => {
				let url = `${urlZw}&funcionalidadeNome=LANCAMENTO_BRINDE&jspPage=lancarBrindeClienteCons.jsp&codPessoa=${this.dadosPessoais.codigoPessoa}`;
				url += `&matriculaCliente=${this.matricula}&prepararTelaLancarBrinde=true&menu=true&origem=angular`;
				this.abrirPopup(url, "Lançamento de brinde", 800, 595);
			});
	}

	enviarEmailConfirmacaoApp(): void {
		if (!this.dadosPessoais.emails || this.dadosPessoais.emails.length <= 0) {
			this.notificationService.error(
				"É necessário que o aluno tenha um e-mail cadastrado."
			);
			return;
		}

		const traducoes = this.traducoes;
		const modal = this.pactoModal.confirm(
			traducoes.getLabel("confimUserTitle"),
			traducoes.getLabel("confimUserBody"),
			traducoes.getLabel("confimUserButton")
		);

		modal.result.then(
			() => {
				this.alunoService.reenviarEmailConfirmacaoApp(this.aluno.id).subscribe(
					(response) => {
						if (
							response &&
							(response.toString().toUpperCase().includes("ERRO") ||
								response.toString().includes("invalido"))
						) {
							this.notificationService.error(response.toString());
						} else {
							this.notificationService.success(
								traducoes.getLabel("confimUserSuccess")
							);
						}
					},
					(httpResponseError) => {
						if (httpResponseError.error) {
							this.notificationService.error(
								httpResponseError.error.meta.message
							);
						} else {
							this.notificationService.error(httpResponseError.meta.message);
						}
					}
				);
			},
			() => {}
		);
	}

	getModalidades(modalidades): string[] {
		return [...new Set(modalidades.map((item) => item.nome))] as string[];
	}

	getFormatedModalidades(modalidades): string[] {
		return [...new Set(modalidades.map((item) => item.nome))].slice(
			1,
			modalidades.length
		) as string[];
	}

	getFormatedPlanos(planos): any[] {
		if (!planos) {
			return [];
		}

		return [...new Set(planos.map((item) => item.descricaoPlano))].slice(
			1,
			planos.length
		) as any[];
	}

	changeAvatar() {
		const dialogRef = this.matDialog.open(ModalImagemComponent, {
			width: "800px",
			height: "auto",
			autoFocus: false,
			panelClass: "modal",
			data: this.dadosPessoais,
		});

		dialogRef.afterClosed().subscribe((result) => {
			if (result === true) {
				this.loadData();
			}
		});
	}

	getVinculos(vinculos: any[]): any {
		if (!vinculos) {
			return {
				PR: [],
				CO: [],
				TW: [],
			};
		}

		const data = vinculos.reduce(
			(
				group: {
					[key: string]: any[];
				},
				item
			) => {
				if (!group[item.tipoVinculo]) {
					group[item.tipoVinculo] = [];
				}

				group[item.tipoVinculo].push(item);

				return group;
			},
			{}
		);

		Object.keys(data).forEach((key) =>
			data[key].sort((a, b) => {
				if (a.colaborador < b.colaborador) {
					return -1;
				}
				if (a.colaborador > b.colaborador) {
					return 1;
				}
				return 0;
			})
		);

		return data;
	}

	getTooltipVinculos(vinculos: any[]): any {
		if (!vinculos) {
			return [];
		}
		return [...new Set(vinculos.map((item) => item.colaborador))].slice(
			1,
			vinculos.length
		) as string[];
	}

	getMessage(aviso): string {
		if (!aviso) {
			return null;
		}

		switch (aviso.tipoMensagem) {
			case "AM":
				if (!aviso.mensagem.toLowerCase().includes("aviso médico:")) {
					return `Aviso médico: ${aviso.mensagem}`;
				}
				return aviso.mensagem;
			case "AA":
				if (!aviso.mensagem.toLowerCase().includes("aviso ao consultor:")) {
					return `Aviso ao consultor: ${aviso.mensagem}`;
				}
				return aviso.mensagem;
			case "OB":
				return aviso.mensagem.replace("<br/>", "").replace("<br>", "");
			default:
				return aviso.mensagem;
		}
	}

	openModalCobrancaAutomatica(): void {
		const openModalBloquearCobranca = () => {
			const modal: PactoModalRef = this.pactoModal.open(
				this.cobrancaAutorizada
					? "Bloquear cobrança automática"
					: "Autorizar cobrança automática",
				ModalCobrancaAutomaticaComponent,
				PactoModalSize.MEDIUM,
				"ds3-modal-cobranca-automatica"
			);
			modal.componentInstance.autorizada = this.cobrancaAutorizada;
			modal.componentInstance.pessoa = this.dadosPessoais.codigoPessoa;
			modal.componentInstance.cobrancaAutomatica.subscribe((res) => {
				if (res.status === "bloqueada") {
					// this.aluno.data_bloqueio_cobranca_automatica = res.data
					//     ? moment(res.data).format('DD/MM/YYYY')
					//     : moment(new Date()).format('DD/MM/YYYY');
					this.cobrancaAutorizada = false;
				} else {
					this.cobrancaAutorizada = true;
				}

				if (res.hintExibir) {
					// Verificando se o 'hintExibir' foi emitido
					this.dadosPagamentoCliente.hintExibir = res.hintExibir; // Atualizando o hintExibir
				}
				this.cd.detectChanges();
			});
		};

		this.autorizarAcessoService
			.validarPermissaoUsuarioLogado(
				this.sessionService.chave,
				this.sessionService.loggedUser.usuarioZw,
				this.sessionService.empresaId,
				"PermiteBloquearDesbloquearClienteCobrancaAutomatica",
				"9.64 - Permite bloquear ou desbloquear cobrança automática do cliente"
			)
			.subscribe(
				(response) => {
					// retorna o código do usuário caso tenha a permissao
					openModalBloquearCobranca();
				},
				(error) => {
					// dá erro caso não tenha
					const modalConfirmacao: any = this.matDialog.open(
						DialogAutorizacaoAcessoComponent,
						{
							disableClose: true,
							id: "autorizacao-acesso",
							autoFocus: false,
						}
					);
					modalConfirmacao.componentInstance.form
						.get("usuario")
						.patchValue(this.sessionService.loggedUser.username);

					modalConfirmacao.componentInstance.confirm.subscribe((result) => {
						this.autorizarAcessoService
							.validarPermissao(
								this.sessionService.chave,
								result.data.usuario,
								result.data.senha,
								"PermiteBloquearDesbloquearClienteCobrancaAutomatica",
								"9.64 - Permite bloquear ou desbloquear cobrança automática do cliente",
								this.sessionService.empresaId.toString()
							)
							.subscribe(
								(response: any) => {
									openModalBloquearCobranca();
									modalConfirmacao.close(null);
								},
								({ error = {} }) => {
									const errorMsg =
										error.meta && error.meta.message
											? error.meta.message
											: `Usuário não possui permissão: 9.64 - Permite bloquear ou desbloquear cobrança automática do cliente.`;
									modalConfirmacao.close(null);
									this.notificationService.error(errorMsg);
								}
							);
					});
				}
			);
	}

	messageIsHTML(aviso: any = {}): boolean {
		return (
			aviso.mensagem &&
			aviso.mensagem &&
			(aviso.mensagem.includes("<title>") ||
				aviso.mensagem.includes("!DOCTYPE"))
		);
	}

	correctToHtml(aviso: any) {
		if (!aviso || !aviso.mensagem) {
			return null;
		}

		const prefix = ["AA", "AM"];
		const prefixEnum = {
			AA: "Aviso ao consultor:",
			AM: "Aviso médico:",
		};
		const messagePrefix = prefix.includes(aviso.tipoMensagem)
			? prefixEnum[aviso.tipoMensagem]
			: "";
		const mensagem = aviso.mensagem;
		const novomsg = mensagem
			.replaceAll(
				'<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">',
				""
			)
			.replaceAll("<head>", "")
			.replaceAll("<title>Untitled document</title>", "")
			.replaceAll("<body>", "")
			.replaceAll("<html>", "")
			.replaceAll("<p>", "")
			.replaceAll("</p>", "")
			.replaceAll("<p>Untitled document</p>", "")
			.replaceAll("Untitled document", "")
			.replaceAll("</head>", "")
			.replaceAll("\n\n", "\n")
			.replaceAll("\n\n", "\n")
			.replaceAll("\n\n", "\n")
			.replaceAll("\n\n", "\n")
			.replaceAll("\n\n", "\n");
		return `${messagePrefix ? `${messagePrefix}` : ""} ${novomsg}`;
	}

	permiteVisualizarClienteTreino() {
		const temPermissaoAluno = this.sessionService.recursos.get(
			PerfilAcessoRecursoNome.ALUNOS
		);
		if (this.permiteVisualizarCliente() || temPermissaoAluno.consultar) {
			if (
				!this.permiteConsultarTodasEmpresas() &&
				this.dadosPessoais.empresa.codigo !==
					this.sessionService.currentEmpresa.codigo
			) {
				this.notificationService.error(
					'Você não tem a permissão "9.50 - Consultar alunos e caixa em aberto de todas as empresas" para ver alunos de outras unidades. ' +
						"Por favor, peça ao administrador para liberar essa permissão " +
						"em 'Perfil de Acesso ADM' > '9.50 - Consultar alunos e caixa em aberto de todas as empresas'.",
					{
						timeout: 7000,
						bodyMaxLength: 300,
					}
				);
				return false;
			}
			const temFuncionalidadedeConsultarAlunosOutrasCarteiras =
				this.permissaoService.temFuncionalidadeTreino(
					PerfilAcessoFuncionalidadeNome.VER_ALUNOS_OUTRAS_CARTEIRAS
				);
			if (temFuncionalidadedeConsultarAlunosOutrasCarteiras) {
				return true;
			} else if (this.possuiVinculoProfessorTW()) {
				return true;
			} else {
				console.error(
					"sem permissão! codColaborador: " +
						this.sessionService.loggedUser.professorResponse.id +
						" lista vinculos: ",
					this.dadosPlano.vinculos
				);
				this.notificationService.error(
					"Você não tem permissão para Acessar alunos de outras carteiras. " +
						"Por favor, solicite ao administrador a liberação dessa permissão, " +
						"que está localizada em ‘Perfil de acesso treino’ > 'Aba aluno' > 'Grupo funcionalidade'.",
					{
						timeout: 7000,
						bodyMaxLength: 300,
					}
				);
				return false;
			}
		} else {
			this.notificationService.error(
				'Você não possui a permissão "2.04 - Clientes". ' +
					"Por favor, entre em contato com o administrador para solicitar a liberação dessa permissão. " +
					"Você pode encontrá-la em 'Perfil de Acesso ADM' > '2.04 - Clientes'.",
				{
					timeout: 7000,
					bodyMaxLength: 300,
				}
			);
			return false;
		}
	}

	possuiVinculoProfessorTW(): boolean {
		// o valor de id retornado pelo validatetoken do treino é o codigoColaborador do professor
		return (
			(this.dadosPlano.vinculos || []).filter(
				(x) =>
					String(x.codigoColaborador) ===
					String(this.sessionService.loggedUser.professorResponse.id)
			).length > 0
		);
	}

	ngAfterViewInit(): void {
		this.limparPactoCatTolltip();
		this.permissaoCard = this.permissaoService.temPermissaoAdm("13.13");
		this.permissaoAvisos = this.permissaoService.temPermissaoAdm("13.14");
		this.permissaoVerificarCliente =
			this.permissaoService.temPermissaoAdm("1.13");
		this.permissaoCliente2_30 =
			this.sessionService.perfilUsuarioAdm.perfilUsuario.recursos.find(
				(r) => r.referenciaRecurso === "2.30"
			);
		this.permissaoCliente2_04 =
			this.sessionService.perfilUsuarioAdm.perfilUsuario.recursos.find(
				(r) => r.referenciaRecurso === "2.04"
			);
		this.permissaoCliente2_29 =
			this.sessionService.perfilUsuarioAdm.perfilUsuario.recursos.find(
				(r) => r.referenciaRecurso === "2.29"
			);
		this.permissaoCliente2_36 =
			this.sessionService.perfilUsuarioAdm.perfilUsuario.recursos.find(
				(r) => r.referenciaRecurso === "2.36"
			);
		this.permissaoCliente9_50 =
			this.sessionService.perfilUsuarioAdm.perfilUsuario.recursos.find(
				(r) => r.referenciaRecurso === "9.50"
			);

		this.statusBadges.changes
			.pipe(startWith(this.statusBadges))
			.subscribe((badges: QueryList<ElementRef<HTMLElement>>) => {
				if (badges.toArray().length > 0) {
					badges.toArray().forEach((badge) => {
						let timer;

						badge.nativeElement.addEventListener("mouseover", () => {
							const overflowWidth = badge.nativeElement.scrollWidth;
							const currentWidth = badge.nativeElement.clientWidth;
							clearInterval(timer);

							let w = currentWidth;
							timer = setInterval(function () {
								const animationComplete = w === overflowWidth;
								if (animationComplete) {
									clearInterval(timer);
								} else {
									w = w > overflowWidth ? overflowWidth : w + 6;
									badge.nativeElement.style.width = `${w}px`;
								}
							}, 10);
						});

						badge.nativeElement.addEventListener("mouseout", () => {
							const currentWidth = badge.nativeElement.clientWidth;
							clearInterval(timer);

							let w = currentWidth;
							timer = setInterval(function () {
								const animationComplete = w === 48;
								if (animationComplete) {
									clearInterval(timer);
								} else {
									w = w < 48 ? 48 : w - 6;
									badge.nativeElement.style.width = `${w}px`;
								}
							}, 10);
						});
					});
				}
			});
		this.permiteVisualizarObservacao();
	}

	permiteCacCliente(): any {
		const permition = this.permissaoCliente2_30;
		const isPermited =
			permition &&
			permition.tipoPermissoes.find(
				(tp) =>
					tp === PerfilRecursoPermissoTipo.CONSULTAR ||
					tp === PerfilRecursoPermissoTipo.INCLUIR ||
					tp === PerfilRecursoPermissoTipo.EDITAR ||
					tp === PerfilRecursoPermissoTipo.EXCLUIR ||
					tp === PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR ||
					tp === PerfilRecursoPermissoTipo.TOTAL
			);
		return isPermited !== undefined && isPermited;
	}

	permiteVisualizarCliente(): any {
		const permition = this.permissaoCliente2_04;
		const isPermited =
			permition &&
			permition.tipoPermissoes.find(
				(tp) =>
					tp === PerfilRecursoPermissoTipo.CONSULTAR ||
					tp === PerfilRecursoPermissoTipo.INCLUIR ||
					tp === PerfilRecursoPermissoTipo.EDITAR ||
					tp === PerfilRecursoPermissoTipo.EXCLUIR ||
					tp === PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR ||
					tp === PerfilRecursoPermissoTipo.TOTAL
			);
		const retor = isPermited !== undefined && isPermited;
		return retor;
	}

	permiteConsultarTodasEmpresas(): any {
		return this.permissaoService.temPermissaoAdm("9.50");
		// const permition = this.permissaoCliente9_50;
		// const isPermited = permition && permition.tipoPermissoes.find(tp =>
		// 	tp === PerfilRecursoPermissoTipo.CONSULTAR
		// 	|| tp === PerfilRecursoPermissoTipo.INCLUIR
		// 	|| tp === PerfilRecursoPermissoTipo.EDITAR
		// 	|| tp === PerfilRecursoPermissoTipo.EXCLUIR
		// 	|| tp === PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR
		// 	|| tp === PerfilRecursoPermissoTipo.TOTAL
		// );
		// const retor = (isPermited !== undefined && isPermited);
		// return retor;
	}

	permiteEditarVinculo(): any {
		const permition = this.permissaoCliente2_29;
		const isPermited =
			permition &&
			permition.tipoPermissoes.find(
				(tp) =>
					tp === PerfilRecursoPermissoTipo.EDITAR ||
					tp === PerfilRecursoPermissoTipo.TOTAL ||
					tp === PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR
			);
		return isPermited;
	}

	permiteEditarCliente(): any {
		const permition = this.permissaoCliente2_04;
		const isPermited =
			permition &&
			permition.tipoPermissoes.find(
				(tp) =>
					tp === PerfilRecursoPermissoTipo.EDITAR ||
					tp === PerfilRecursoPermissoTipo.TOTAL ||
					tp === PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR
			);
		return isPermited;
	}

	permiteTrocarProfessor(): boolean {
		return this.sessionService.perfilUsuario.funcionalidades.get(
			PerfilAcessoFuncionalidadeNome.TROCAR_PROFESSOR
		);
	}

	possuiPermissaoIncluirOrRetirirClienteRestricao() {
		this.permiteIncluirOrRetirirClienteRestricao =
			this.permissaoService.temPermissaoAdm("9.100");
	}

	cadastrarAppCadastrado() {
		const modalRef = this.pactoModal.open(
			"Sem cadastrado",
			ModalCadastroStatusComponent,
			PactoModalSize.LARGE
		);
		modalRef.componentInstance.bodyText =
			"Seu aluno ainda não possui cadastro em nosso aplicativo. Clique em enviar e-mail para mandar as informações de acesso e instruções de cadastro.";
		modalRef.componentInstance.labelCadastro = "Enviar e-mail";
		modalRef.componentInstance.result.subscribe((data) => {
			if (data.status) {
				this.enviarEmailConfirmacaoApp();
				modalRef.close("executado");
			} else {
				modalRef.close("cancelado");
			}
		});
	}

	cadastrarBiometriaFacial() {
		const modalRef = this.pactoModal.open(
			"Biometria facial",
			ModalCadastroStatusComponent,
			PactoModalSize.LARGE
		);
		modalRef.componentInstance.bodyInfo =
			"Observação: Essa informação estará disponível apenas se a empresa utiliza os recursos de biometria FacialPacto.";
		modalRef.componentInstance.bodyText =
			"Seu aluno não possui a biometria facial cadastrada. O que deseja fazer?";
		modalRef.componentInstance.labelCadastro = "Quero aprender a cadastrar";
		modalRef.componentInstance.result.subscribe((data) => {
			if (data.status) {
				window.open(
					"https://pactosolucoes.com.br/ajuda/conhecimento/como-cadastrar-facial/",
					"blank"
				);
				modalRef.close("executado");
			} else {
				modalRef.close("cancelado");
			}
		});
		modalRef.componentInstance.actionButton = "openWiki()";
	}

	cadastrarBiometriaDigital() {
		const modalRef = this.pactoModal.open(
			"Biometria digital",
			ModalCadastroStatusComponent,
			PactoModalSize.LARGE
		);
		modalRef.componentInstance.bodyInfo =
			"Observação: Essa informação estará disponível apenas se a empresa utiliza os recursos de biometria da Neokoros.";
		modalRef.componentInstance.bodyText =
			"Seu aluno não possui a biometria digital cadastrada. O que deseja fazer?";
		modalRef.componentInstance.labelCadastro = "Quero aprender a cadastrar";
		modalRef.componentInstance.result.subscribe((data) => {
			if (data.status) {
				window.open(
					"https://pactosolucoes.com.br/ajuda/conhecimento/como-cadastrar-digital-biometria-no-zillyon-acesso/",
					"blank"
				);
				modalRef.close("executado");
			} else {
				modalRef.close("cancelado");
			}
		});
	}

	cadastrarDadosVerificados() {
		const modalRef = this.pactoModal.open(
			"Verificação de dados",
			ModalCadastroStatusComponent,
			PactoModalSize.LARGE
		);
		modalRef.componentInstance.bodyText =
			"É importante realizar uma verificação dos dados do cliente para garantir que todas as suas informações estejam completas. Isso assegura um serviço eficaz e preciso.";
		modalRef.componentInstance.labelCadastro = "Dados básicos ";
		modalRef.componentInstance.result.subscribe((data) => {
			if (data.status) {
				this.goDadosPessoais();
				modalRef.close("executado");
			} else {
				modalRef.close("cancelado");
			}
		});
	}

	cadastrarParQ() {
		const modalRef = this.pactoModal.open(
			"Par-Q",
			ModalCadastroStatusComponent,
			PactoModalSize.LARGE
		);
		modalRef.componentInstance.bodyText =
			"Seu aluno não possui o atestado de prontidão para atividades físicas  (ParQ) assinado ou se assinado o resultado não foi positivo. O que deseja fazer?";
		modalRef.componentInstance.labelCadastro = "Saber mais";
		modalRef.componentInstance.result.subscribe((data) => {
			if (data.status) {
				window.open(
					"https://pactosolucoes.com.br/ajuda/conhecimento/formulario-par-q/",
					"blank"
				);
				modalRef.close("executado");
			} else {
				modalRef.close("cancelado");
			}
		});
	}

	goToAtestadoDeAptidaoFisica() {
		const modalRef = this.pactoModal.open(
			"Atestado de aptidão física",
			ModalAtestadoAptidaoFisicaComponent,
			PactoModalSize.LARGE
		);
	}

	goToCobranca() {
		const modalRef = this.pactoModal.open(
			"Cobrança",
			ModalCobrancaComponent,
			PactoModalSize.LARGE
		);
	}

	isClienteMensagemClick(clienteMensagem): boolean {
		return (
			clienteMensagem.tipoMensagem === "RI" ||
			clienteMensagem.tipoMensagem === "CV" ||
			clienteMensagem.tipoMensagem === "DI" ||
			clienteMensagem.tipoMensagem === "BP" ||
			clienteMensagem.tipoMensagem === "PV" ||
			clienteMensagem.tipoMensagem === "PA"
		);
	}

	abrirClienteMensagemProdutoVencido(clienteMensagem): void {
		const dialogRef = this.pactoModal.open(
			"Produto vencido- Acesso bloqueado",
			ModalMensagemProdutoVencidoComponent,
			PactoModalSize.LARGE,
			"design-system3-adjust"
		);
		dialogRef.componentInstance.chave = this.sessionService.chave;
		dialogRef.componentInstance.cliente = this.dadosPessoais.codigoCliente;
		dialogRef.componentInstance.pessoa = this.dadosPessoais.codigoPessoa;
		dialogRef.componentInstance.usuario = this.sessionService.codigoUsuarioZw;
		dialogRef.componentInstance.clienteMensagem = clienteMensagem;
		dialogRef.componentInstance.response.subscribe((res) => {
			if (res === "atualizar") {
				this.updateWarnings$.next();
				this.cd.detectChanges();
			} else if (res === "crm") {
				this.abrirContatoAvulso();
			}
		});
	}

	acaoClienteMensagem(clienteMensagem) {
		if (clienteMensagem.tipoMensagem === "RI") {
			// RISCO
			this.abrirContatoAvulso();
		} else if (clienteMensagem.tipoMensagem === "CV") {
			// CARTAO_VENCIDO
			this.goPactoPay();
		} else if (clienteMensagem.tipoMensagem === "PA") {
			// PARCELA_ATRASO
			this.abrirCaixaEmAberto();
		} else if (clienteMensagem.tipoMensagem === "DI") {
			// DADOS_INCOMPLETOS
			this.goDadosPessoais();
		} else if (clienteMensagem.tipoMensagem === "BP") {
			// BOLETIM
			this.abrirBoletimVisita(clienteMensagem);
		} else if (clienteMensagem.tipoMensagem === "PV") {
			// PRODUTO_VENCIDO
			this.abrirClienteMensagemProdutoVencido(clienteMensagem);
		}
	}

	abrirContatoAvulso() {
		this.clientDiscoveryService
			.linkZw(this.sessionService.usuarioOamd, this.sessionService.empresaId)
			.subscribe((urlZw) => {
				let url = `${urlZw}&funcionalidadeNome=CONTATO_AVULSO&codPessoa=${this.dadosPessoais.codigoPessoa}`;
				url += `&matriculaCliente=${this.matricula}&menu=true&codCliente=${this.dadosPessoais.codigoCliente}&origem=angular`;

				this.abrirPopup(url, "ContatoAvulso", 800, 595);
			});
	}

	getSituacao(s: string): string {
		switch (s) {
			case "AE":
				return "ATESTADO";
			case "AT":
				return "ATIVO";
			case "AV":
				return "A VENCER";
			case "NO":
				return "NORMAL";
			case "CR":
				return "FÉRIAS";
			case "DE":
				return "DESISTENTE";
			case "CA":
				return "CANCELADO";
			case "VE":
				return "VENCIDO";
			case "DI":
				return "DIÁRIA";
			case "IN":
				return "INATIVO";
			case "VI":
				return "VISITANTE";
			case "TP":
				return "TOTALPASS";
			case "TV":
				return "VENCIDO";
			case "TR":
				return "TRANCADO";
			case "GY":
				return "GYMPASS";
			case "PE":
				return "FREEPASS";
			default:
				return "OUTRO";
		}
	}

	getGenero(genero: string) {
		if (!genero) {
			return;
		}
		switch (genero) {
			case "AG":
				return "Agênero";
			case "FE":
				return "Feminino";
			case "MA":
				return "Masculino";
			case "NB":
				return "Não-binário";
		}
	}

	voltarTelaAntigaAlunoTreino() {
		this.router.navigateByUrl(
			`/cadastros/alunos/perfil/${this.activatedRoute.snapshot.params["aluno-matricula"]}%3Forigem%3Dbi`
		);
	}

	openModalNivel() {
		const modalRef = this.pactoModal.open(
			"Selecione o nível do cliente",
			ModalNivelClienteComponent,
			PactoModalSize.LARGE
		);
		modalRef.componentInstance.matricula = this.matricula;
		modalRef.result.then((result) => {
			this.aluno.nivel = result;
			this.cd.detectChanges();
		});
	}

	listaPessoas() {
		this.router.navigate(["cadastros", "alunos", "listagem"]);
	}

	openModalRemoverObjecao() {
		const modalRef = this.pactoModal.open(
			"Remover objeção",
			ModalObjecaoDefinitivaComponent,
			PactoModalSize.MEDIUM
		);
		modalRef.componentInstance.dadosPessoais = this.dadosPessoais;
		modalRef.result.then((result) => {
			if (result) {
				this.dadosPessoais.objecao = null;
				this.cd.detectChanges();
			}
		});
	}

	apresentarWhatsApp() {
		for (let i = 0; this.dadosPessoais.telefones.length > i; i++) {
			if (this.dadosPessoais.telefones[i].whatsapp) {
				return true;
			}
		}
		return false;
	}

	clickWhatsApp() {
		let qtdWhats = 0;
		for (let i = 0; this.dadosPessoais.telefones.length > i; i++) {
			if (this.dadosPessoais.telefones[i].whatsapp) {
				qtdWhats = qtdWhats + 1;
			}
		}
		if (qtdWhats > 1) {
			this.openModalContatoTelefone();
		} else {
			const tel = this.dadosPessoais.telefones[0];
			const valorInserido = (tel.ddi ? "+" + tel.ddi : "+55") + tel.numero;
			const valorInput = Number(
				valorInserido
					.replace("(", "")
					.replace(")", "")
					.replace(" ", "")
					.replace("+", "")
					.replace("-", "")
			);
			const target = "https://api.whatsapp.com/send?phone=" + valorInput;
			window.open(target, "_blank");
		}
	}

	sendGymbot() {
		const GymBot = {
			nome: this.dadosPessoais.nome,
			numero: this.dadosPessoais.telefones[0].numero.toString(),
			idEmpresa: this.dadosPessoais.empresa.codigo.toString(),
			idCliente: this.dadosPessoais.codigoCliente.toString(),
			idUsuario: this.sessionService.loggedUser.id.toString(),
			nomeUsuario: this.sessionService.loggedUser.nome.toString(),
		};
		console.log(this.sessionService);

		this.mensagensAvisoService.dispararFluxoGymBot(GymBot).subscribe(
			(resp) => {
				this.notificationService.success("Fluxo Gymbot Enviado");
				this.initDadosImportacao();
			},
			(httpErrorResponse) => {
				const err = httpErrorResponse.error;
				if (err.meta && err.meta.message) {
					this.notificationService.error(err.meta.message);
				}
			}
		);
	}

	sendGymbotPro() {
		const GymBot = {
			nome: this.dadosPessoais.nome,
			numero: this.dadosPessoais.telefones[0].numero.toString(),
			idEmpresa: this.dadosPessoais.empresa.codigo.toString(),
			idCliente: this.dadosPessoais.codigoCliente.toString(),
			idUsuario: this.sessionService.loggedUser.id.toString(),
			nomeUsuario: this.sessionService.loggedUser.nome.toString(),
		};
		console.log(this.sessionService);

		this.mensagensAvisoService.dispararFluxoGymBotPro(GymBot).subscribe(
			(resp) => {
				this.notificationService.success("GymBot Pro Enviado");
				this.initDadosImportacao();
			},
			(httpErrorResponse) => {
				const err = httpErrorResponse.error;
				if (err.meta && err.meta.message) {
					this.notificationService.error(err.meta.message);
				}
			}
		);
	}

	clickEmail() {
		if (this.dadosPessoais.emails.length > 1) {
			this.openModalContatoEmail();
		} else {
			const target = "mailto:" + this.dadosPessoais.emails[0];
			window.open(target, "_blank");
		}
	}

	openModalContatoTelefone() {
		const modalRef = this.pactoModal.open(
			"Selecione um contato",
			ModalContatoClienteComponent,
			PactoModalSize.MEDIUM
		);
		modalRef.componentInstance.dadosPessoais = this.dadosPessoais;
		modalRef.componentInstance.telefone = true;
	}

	openModalContatoEmail() {
		const modalRef = this.pactoModal.open(
			"Selecione um contato",
			ModalContatoClienteComponent,
			PactoModalSize.MEDIUM
		);
		modalRef.componentInstance.dadosPessoais = this.dadosPessoais;
		modalRef.componentInstance.email = true;
	}

	abrirGestaoRecebiveis() {
		this.autorizarAcessoService
			.validarPermissaoUsuarioLogado(
				this.sessionService.chave,
				this.sessionService.loggedUser.usuarioZw,
				this.sessionService.empresaId,
				"GestaoRecebiveis",
				"9.01 - Gestão de Recebíveis"
			)
			.subscribe(
				(response) => {
					this.clientDiscoveryService
						.linkZw(
							this.sessionService.usuarioOamd,
							this.sessionService.empresaId
						)
						.subscribe((urlZw) => {
							let url = `${urlZw}&funcionalidadeNome=RECEBIVEIS&jspPage=gestaoRecebiveis.jsp&codPessoa=${this.dadosPessoais.codigoPessoa}`;
							url += `&matriculaCliente=${this.matricula}`;
							url += `&nomePessoa=${this.dadosPessoais.nome}&origem=angular`;
							window.open(url, "_self");
						});
				},
				(httpResponseError) => {
					this.notificationService.error(httpResponseError.error.meta.message);
				}
			);
	}

	titleSPC() {
		if (this.dadosAuxiliares) {
			const title =
				"Negativado em " +
				moment(this.dadosAuxiliares.dataInclusaoSpc).format("DD/MM/YYYY") +
				".";
			let parcelas = "";
			for (let i = 0; this.dadosAuxiliares.parcelasSpc.length > i; i++) {
				const parcela = this.dadosAuxiliares.parcelasSpc[i];
				const valorFormatado = Number(parcela.valor).toLocaleString("pt-BR", {
					minimumFractionDigits: 2,
					maximumFractionDigits: 2,
				});
				parcelas +=
					"\n" +
					parcela.codigo +
					" - " +
					parcela.descricao +
					" - " +
					valorFormatado;
			}
			return title + "\n" + parcelas;
		}
		return "";
	}

	verificarCliente() {
		this.admLegadoTelaClienteService
			.verificarClienteImportacao(
				this.sessionService.chave,
				this.sessionService.codigoUsuarioZw,
				this.dadosPessoais.codigoCliente
			)
			.subscribe(
				(resp) => {
					this.notificationService.success("Cliente verificado");
					this.initDadosImportacao();
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.message) {
						this.notificationService.error(err.meta.message);
					}
				}
			);
	}

	desverificarCliente() {
		this.admLegadoTelaClienteService
			.desverificarClienteImportacao(
				this.sessionService.chave,
				this.sessionService.codigoUsuarioZw,
				this.dadosPessoais.codigoCliente
			)
			.subscribe(
				(resp) => {
					this.notificationService.success("Verificação removida");
					this.initDadosImportacao();
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.message) {
						this.notificationService.error(err.meta.message);
					}
				}
			);
	}

	goTitularContratoCompartilhado() {
		this.router.navigateByUrl(
			`/cadastros/alunos/perfil/${this.dadosPlano.titularContratoMatricula}%3Forigem%3Dbi`
		);
	}

	limparPactoCatTolltip() {
		try {
			const elementsByClassName =
				document.getElementsByClassName("pacto-cat-tolltip");
			const array = Array.from(elementsByClassName);
			for (const element of array) {
				document.getElementById(element.id).style.visibility = "hidden";
			}
		} catch (e) {
			console.log(e);
		}
	}

	novoContrato() {
		this.router.navigate(
			["pessoas", "perfil-v2", this.matricula, "contratos"],
			{ queryParams: { adicionar: true } }
		);
	}

	novoContratoNew() {
		// if (!this.ableNovoContrato) {
		// 	return;
		// }

		this.autorizarAcessoService
			.validarPermissaoUsuarioLogado(
				this.sessionService.chave,
				this.sessionService.loggedUser.usuarioZw,
				this.sessionService.empresaId,
				"VendaRapidaTelaPadraoLancarContrato",
				"3.40 - Tela (Venda Rápida) como tela padrão para lançamento de contrato"
			)
			.subscribe(
				(response) => {
					this.abrirVendaRapidaAntiga();
				},
				(httpResponseError) => {
					this.admLegadoTelaClienteService
						.getListaContratoRematricularERenovar(
							this.sessionService.chave,
							this.dadosPessoais.codigoCliente,
							this.sessionService.empresaId
						)
						.subscribe((response) => {
							if (response.content.length > 0) {
								const dialogRef = this.dialogService.open(
									"Aviso",
									ModalContratosRenovarERematricularComponent,
									PactoModalSize.LARGE
								);
								dialogRef.componentInstance.contratos = response.content;
								dialogRef.componentInstance.codCliente =
									this.dadosPessoais.codigoCliente;
								dialogRef.componentInstance.update.subscribe((res) => {
									if (res === "novocontrato") {
										this.lancarNovoContrato();
									} else if (res.startsWith("renovar")) {
										this.renovarRematricular(
											res.replace("renovar", ""),
											"renovar"
										);
									} else if (res.startsWith("rematricular")) {
										this.renovarRematricular(
											res.replace("rematricular", ""),
											"rematricular"
										);
									}
								});
							} else {
								this.admCoreApiNegociacaoService
									.negociacaoHabilitada()
									.subscribe((rest) => {
										if (rest) {
											this.abrirNovaNegociacao("novo");
											return;
										} else {
											this.clientDiscoveryService
												.linkZw(
													this.sessionService.usuarioOamd,
													this.sessionService.empresaId
												)
												.subscribe((result) => {
													const url = `${result}&codCliente=${this.dadosPessoais.codigoCliente}&operacaoClienteEnumName=NOVO_CONTRATO&isContratoOperacao=true`;
													window.open(url, "_self");
													this.cd.detectChanges();
												});
										}
									});
							}
						});
				}
			);
	}

	abrirVendaRapidaAntiga() {
		this.clientDiscoveryService
			.linkZw(this.sessionService.usuarioOamd, this.sessionService.empresaId)
			.subscribe((result) => {
				window.open(
					`${result}&codCliente=${this.dadosPessoais.codigoCliente}&operacaoClienteEnumName=NOVO_CONTRATO_VENDA_RAPIDA&isContratoOperacao=true`,
					"_self"
				);
				this.cd.detectChanges();
			});
	}

	lancarNovoContrato() {
		this.autorizarAcessoService
			.validarPermissaoUsuarioLogado(
				this.sessionService.chave,
				this.sessionService.loggedUser.usuarioZw,
				this.sessionService.empresaId,
				"VendaRapidaTelaPadraoLancarContrato",
				"3.40 - Tela (Venda Rápida) como tela padrão para lançamento de contrato"
			)
			.subscribe(
				(response) => {
					this.abrirVendaRapidaAntiga();
				},
				(httpResponseError) => {
					this.admCoreApiNegociacaoService
						.negociacaoHabilitada()
						.subscribe((rest) => {
							if (rest) {
								this.abrirNovaNegociacao("novo");
								return;
							} else {
								this.clientDiscoveryService
									.linkZw(
										this.sessionService.usuarioOamd,
										this.sessionService.empresaId
									)
									.subscribe((result) => {
										window.open(
											`${result}&codCliente=${this.dadosPessoais.codigoCliente}&operacaoClienteEnumName=NOVO_CONTRATO&isContratoOperacao=true&contratoConcomitante=true`,
											"_self"
										);
									});
							}
						});
				}
			);
	}

	renovarRematricular(codContrato, operacao) {
		this.autorizarAcessoService
			.validarPermissaoUsuarioLogado(
				this.sessionService.chave,
				this.sessionService.loggedUser.usuarioZw,
				this.sessionService.empresaId,
				"VendaRapidaTelaPadraoLancarContrato",
				"3.40 - Tela (Venda Rápida) como tela padrão para lançamento de contrato"
			)
			.subscribe(
				(response) => {
					this.abrirVendaRapidaAntiga();
				},
				(httpResponseError) => {
					this.admCoreApiNegociacaoService
						.negociacaoHabilitada()
						.subscribe((rest) => {
							if (rest) {
								this.abrirNovaNegociacao(operacao + "_" + codContrato);
								return;
							} else {
								if (operacao === "rematricular") {
									this.clientDiscoveryService
										.linkZw(
											this.sessionService.usuarioOamd,
											this.sessionService.empresaId
										)
										.subscribe((result) => {
											const url = `${result}&contratoRenovar=${codContrato}&codCliente=${this.dadosPessoais.codigoCliente}&operacaoClienteEnumName=REMATRICULAR_RENOVAR_CONTRATO&isContratoOperacao=true&rematricular=true`;
											window.open(url, "_self");
										});
								} else if (operacao === "renovar") {
									this.clientDiscoveryService
										.linkZw(
											this.sessionService.usuarioOamd,
											this.sessionService.empresaId
										)
										.subscribe((result) => {
											const url = `${result}&contratoRenovar=${codContrato}&codCliente=${this.dadosPessoais.codigoCliente}&operacaoClienteEnumName=REMATRICULAR_RENOVAR_CONTRATO&isContratoOperacao=true&renovar=true`;
											window.open(url, "_self");
										});
								}
							}
						});
				}
			);
	}

	abrirNovaNegociacao(linha) {
		const url = this.layoutNavigationService.createNovaPlataformaUrl(
			PlataformaModulo.NZW,
			`/adm/negociacao/contrato/${this.dadosPessoais.matricula}/${linha}`
		);
		window.open(`${url}`, "_self");
	}

	getLabelButtonAddContrato(str) {
		return "Novo contrato";
	}

	atualizarBV() {
		this.clientDiscoveryService
			.linkZw(this.sessionService.usuarioOamd, this.sessionService.empresaId)
			.subscribe((urlZw) => {
				let url = `${urlZw}&funcionalidadeNome=ATUALIZAR_BV&jspPage=tela4.jsp&codPessoa=${this.dadosPessoais.codigoPessoa}`;
				url += `&matriculaCliente=${this.matricula}`;
				url += `&codCliente=${this.dadosPessoais.codigoCliente}`;
				url += `&nomePessoa=${this.dadosPessoais.nome}&prepararTelaAtualizarBV=true&menu=true&origem=angular`;
				window.open(url, "_self");
			});
	}

	loadIntegracaoMqv() {
		this.mqv = this.configCache.configuracoesIntegracoesListaMQV;
		const find = this.mqv.find(
			(x) => x.empresa === Number(this.sessionService.empresaId)
		);
		const tokenMqv = find ? find.token : null;
		this.apresentarBtnMQV = tokenMqv !== null && tokenMqv.length > 0;
		this.cd.detectChanges();
	}

	abrirRelatorioMQV() {
		// Estrutura da url mqv: 'https://mqv.institutodoalivio.com.br/CRM/ADM/PerfilAdm/0/0/TOKEN/EMAIL/CELULAR'
		let urlMq = "https://mqv.institutodoalivio.com.br/CRM/ADM/PerfilAdm/0/0";
		const find = this.mqv.find(
			(x) => x.empresa === Number(this.sessionService.empresaId)
		);
		const tokenMqv = find ? find.token : null;
		if (tokenMqv !== null && tokenMqv.length > 0) {
			urlMq += "/" + tokenMqv;

			if (this.dadosPessoais.emails && this.dadosPessoais.emails.length > 0) {
				urlMq += "/" + this.dadosPessoais.emails[0];
			} else {
				urlMq += "/N";
			}

			if (
				this.dadosPessoais.telefones &&
				this.dadosPessoais.telefones.length > 0
			) {
				urlMq +=
					"/" +
					this.dadosPessoais.telefones[0].numero
						.replace("(", "")
						.replace(")", "")
						.replace("-", "")
						.replace(" ", "");
			} else {
				urlMq += "/0";
			}
			window.open(urlMq, "_blank");
		} else {
			this.notificationService.error(
				"Para prosseguir, é necessário que esta unidade possua o token MQV cadastrado."
			);
		}
	}

	loadIntegracaoMgb() {
		this.mgb = this.configCache.configuracoesIntegracoesListaMGB;
		const find = this.mgb.find(
			(x) => x.empresa === Number(this.sessionService.empresaId)
		);
		const tokenMbg = find ? find.token : null;
		this.apresentarBtnSyncMGB = tokenMbg !== null && tokenMbg.length > 0;
		this.cd.detectChanges();
	}

	sincronizarAlunoMgb() {
		this.admLegadoTelaClienteService
			.sincronizarAlunoMgb(
				this.sessionService.chave,
				String(this.dadosPessoais.codigoCliente),
				String(this.sessionService.empresaId)
			)
			.subscribe({
				error: (error) => {
					const msg = error.error.mensagem.split("Mensagem tecnica: Erro:");
					if (msg.length === 2) {
						this.notificationService.error(msg[1]);
					} else {
						this.notificationService.error(error.error.mensagem);
					}
				},
				next: (response) => {
					this.notificationService.success(
						"O cadastro do aluno foi sincronizado com sucesso"
					);
				},
			});
	}

	openModalReposicoesDeAulaColetiva() {
		const modalRef = this.pactoModal.open(
			"Histórico de reposições de aulas coletivas",
			ModalHistoricoReposicaoAulasColetivasComponent,
			PactoModalSize.LARGE
		);
		modalRef.componentInstance.dadosPessoais = this.dadosPessoais;
	}

	incluirOuRetirarClienteRestricao() {
		if (this.permiteIncluirOrRetirirClienteRestricao) {
			if (!this.isPossuiRestricao) {
				const modal = this.pactoModal.open(
					"Incluir na lista de clientes com restrições",
					ModalClienteRestricaoComponent,
					PactoModalSize.LARGE
				);
				modal.componentInstance.dadosPessoais = this.dadosPessoais;
				if (modal.result) {
					modal.result.then((msg) => {
						if (msg === "sucesso") {
							this.isPossuiRestricao = true;
							this.cd.detectChanges();
						}
					});
				}
			} else {
				const chaveEmpresa = this.sessionService.chave;
				const codigoEmpresa = parseInt(this.sessionService.codigoEmpresa, 10);

				const restricoesRemover: ClienteRestricao[] =
					this.clienteRestricoes.filter(
						(cr) =>
							cr.tipo === "RM" &&
							cr.chaveEmpresa === chaveEmpresa &&
							cr.codigoEmpresa === codigoEmpresa
					);

				const modal = this.pactoModal.confirm(
					"Retirar cliente da lista de clientes com restrições",
					"Confirmar a retirada do cliente da lista de clientes com restrições?",
					"Confirmar"
				);
				if (modal.result) {
					modal.result.then(() => {
						const cpfSemMascara = this.dadosPessoais.cpf.replace(/[^\d]+/g, "");
						this.admCoreApiClienteRestricaoService
							.delete(
								cpfSemMascara,
								chaveEmpresa,
								codigoEmpresa,
								TipoClienteRestricaoEnum.RESGRISTRO_MANUAL_TELA_CLIENTE
							)
							.pipe(
								switchMap(() => {
									return this.admCoreApiClienteRestricaoService.atualizarRestricoesInadimplencia(
										cpfSemMascara
									);
								}),
								switchMap(() => {
									return this.admCoreApiClienteRestricaoService.findByCpf(
										cpfSemMascara
									);
								})
							)
							.subscribe(
								(clienteRestricoesResponse) => {
									this.clienteRestricoes = clienteRestricoesResponse;
									try {
										if (this.clienteRestricoes.length === 0) {
											this.isPossuiRestricao = false;
											this.notificationService.success(
												"Operação realiza com sucesso"
											);
										} else {
											this.isPossuiRestricao = true;
											// Verificar se removeu restrição manual da unidade
											if (restricoesRemover && restricoesRemover.length > 0) {
												const listRestricoesNaUnidade =
													this.clienteRestricoes.filter(
														(cr) =>
															cr.tipo === "RM" &&
															cr.chaveEmpresa === chaveEmpresa &&
															cr.codigoEmpresa === codigoEmpresa
													);
												if (listRestricoesNaUnidade.length === 0) {
													this.notificationService.success(
														"A restrição lançada manualmente na unidade: " +
															this.sessionService.currentEmpresa.nome +
															" foi removida!"
													);
												} else {
													throw new Error("Falha ao retirar restrição!");
												}
											}

											// Verificar se ainda está com restrição por inadimplencia
											const restricoesInadimplencia =
												this.clienteRestricoes.filter(
													(cr) =>
														cr.tipo === TipoClienteRestricaoEnum.INADINPLENCIA
												);
											if (
												restricoesInadimplencia &&
												restricoesInadimplencia.length > 0
											) {
												this.isPossuiRestricaoPorInadimplencia = true;
												throw new Error(
													"Cliente possui restrição(es) por inadimplência na(s) unidade(s): " +
														this.unidadesClienteRestricao(
															restricoesInadimplencia
														) +
														" para retira-la(s), solicitamos que o cliente entre em contato com a(s) referida(s) unidade(s) para regularizar suas pendencias."
												);
											}

											// Verificar se possui restrições de outras unidades
											const restricoesManuaisOutrasUnidades =
												this.clienteRestricoes.filter(
													(cr) =>
														cr.tipo === "RM" &&
														(cr.chaveEmpresa !== chaveEmpresa ||
															cr.codigoEmpresa !== codigoEmpresa)
												);
											if (
												restricoesManuaisOutrasUnidades &&
												restricoesManuaisOutrasUnidades.length > 0
											) {
												throw new Error(
													"Cliente possui restrição(es) em outra(s) unidade(s): " +
														this.unidadesClienteRestricao(
															restricoesManuaisOutrasUnidades
														) +
														" para retira-la(s), solicitamos que o cliente entre em contato com a(s) referida(s) unidade(s) para regularizar suas pendencias."
												);
											}
										}
									} catch (error) {
										this.notificationService.error(error.message);
									} finally {
										this.notificarClienteRestricaoService.notifyAboutChange();
										const menuDropDown = document.getElementById(
											"dropdown-mais-content"
										);
										if (menuDropDown) {
											menuDropDown.classList.remove("show");
										}
									}
								},
								(httpResponseError) => {
									this.notificationService.error(
										`Falha ao retirar restrição! ${httpResponseError}`
									);
								}
							);
					});
				}
			}
		} else {
			this.notificationService.error(
				"Você não possui permissão para realizar esta operação"
			);
		}
	}

	get utilizarGestaoClienteComRestricao() {
		return this.permissaoService.temConfiguracaoEmpresaAdm(
			"utilizaGestaoClientesComRestricoes"
		);
	}

	get isConfigPactoPrint() {
		return this.permissaoService.temConfiguracaoEmpresaAdm(
			"utilizarPactoPrint"
		);
	}

	get toolTipBtnIncluirOuRetirarClienteRestricao() {
		if (this.isPossuiRestricaoPorInadimplencia) {
			const unidades = this.unidadesClienteRestricao(this.clienteRestricoes);
			if (unidades !== "") {
				return (
					"Cliente possui restrições por inadimplencia na(s) unidade(s): " +
					unidades +
					" para retira-la(s), solicitamos que o cliente entre em contato com a(s) referida(s) unidade(s) para regularizar suas pendências."
				);
			}
		}
		return this.labelBtnClienteRestricao;
	}

	unidadesClienteRestricao(
		listClienteRestricao: Array<ClienteRestricao>
	): string {
		if (!listClienteRestricao || listClienteRestricao.length === 0) {
			return "";
		}
		let unidades = "";
		listClienteRestricao.forEach((cr) => {
			if (!unidades.includes(cr.nomeEmpresa)) {
				unidades += unidades === "" ? cr.nomeEmpresa : ", " + cr.nomeEmpresa;
			}
		});
		return unidades;
	}

	get labelBtnClienteRestricao(): string {
		return !this.isPossuiRestricao
			? "Incluir na lista de clientes com restrições"
			: "Retirar da lista de clientes com restrições";
	}

	get parqNaoRespondido(): boolean {
		return (
			!this.parqNegativo &&
			!this.parqPositivo &&
			this.aluno &&
			this.aluno.infoParQ &&
			this.aluno.infoParQ === "NAO_ASSINADO"
		);
	}

	get parqPositivo(): boolean {
		return (
			(this.aluno &&
				this.aluno.infoParQ &&
				this.aluno.infoParQ === "POSITIVO") ||
			(this.dadosPessoais && this.dadosPessoais.parqPositivo)
		);
	}

	get parqNegativo(): boolean {
		return (
			!this.parqPositivo &&
			this.aluno &&
			this.aluno.infoParQ &&
			this.aluno.infoParQ === "NEGATIVO"
		);
	}

	getTimezoneOffset(date) {
		return moment(date).utc().format("DD/MM/YYYY");
	}

	private possuiPermissaoVerAbaContrato() {
		this.temPermissaoVerAbaContrato =
			this.permissaoService.temPermissaoAdm("13.00");
	}

	openDetalhamentoContratos() {
		const dialogRef = this.dialogService.open(
			"Resumo do contrato",
			MdlClienteResumoContratoComponent,
			undefined,
			"mdl-cliente-resumo-contrato"
		);
		dialogRef.componentInstance.contratosAtivos = this.planosAtivos;
		dialogRef.componentInstance.dadosPessoais = this.dadosPessoais;
	}

	exibirCarteirinhaNovaAba(base64Data: string) {
		const byteCharacters = atob(base64Data);
		const byteArray = new Uint8Array(byteCharacters.length);

		for (let i = 0; i < byteCharacters.length; i++) {
			byteArray[i] = byteCharacters.charCodeAt(i);
		}

		const blob = new Blob([byteArray], { type: "application/pdf" });
		const urlPdf = window.URL.createObjectURL(blob);

		window.open(urlPdf);
	}

	abrirMenuMais() {
		try {
			setTimeout(() => {
				const menu = document.getElementById("pch-btn-mais");
				if (!menu.classList.contains("opened")) {
					menu.click();
				}
				setTimeout(() => {
					this.fecharMenuMais();
				}, 5000);
			}, 1000);
		} catch (e) {
			console.log(e);
		}
	}

	fecharMenuMais() {
		try {
			const menu = document.getElementById("dropdown_btn_mais_opcoes");
			const btnMais = document.getElementById("pch-btn-mais");
			if (menu.classList.contains("show")) {
				btnMais.click();
			}
		} catch (e) {
			console.log(e);
		}
	}

	acaoAtivarAcessoConvidado(): void {
		const modal = this.pactoModal.confirm(
			"Acesso do convidado",
			"Deseja ativar o acesso do convidado?",
			"Sim"
		);

		modal.result.then(
			() => {
				this.admLegadoTelaClienteService
					.ativarAcessoConvidado(
						this.sessionService.chave,
						this.dadosPessoais.codigoCliente,
						this.sessionService.loggedUser.usuarioZw
					)
					.subscribe(
						(resp) => {
							console.log(resp);
							if (resp.content && resp.content === "ok") {
								this.notificationService.success("Acesso ativado");
								this.loadData();
								this.cd.detectChanges();
							} else {
								this.notificationService.error(resp);
							}
						},
						(httpResponseError) => {
							if (httpResponseError.error) {
								this.notificationService.error(
									httpResponseError.error.meta.message
								);
							} else {
								this.notificationService.error(httpResponseError.meta.message);
							}
						}
					);
			},
			() => {}
		);
	}

	get habilitaNovoContrato(): boolean {
		return (
			this.temPermissaoVerAbaContrato &&
			this.apresentarMenuContrato &&
			this.ableNovoContrato
		);
	}

	loadClienteRestricoes() {
		if (
			this.utilizarGestaoClienteComRestricao &&
			!isNullOrUndefinedOrEmpty(this.dadosPessoais.cpf)
		) {
			const cpfSemMascara = this.dadosPessoais.cpf.replace(/\D/g, "");
			this.admCoreApiClienteRestricaoService
				.findByCpf(cpfSemMascara)
				.subscribe((clienteRestricoesResponse) => {
					this.clienteRestricoes = clienteRestricoesResponse;
					if (this.clienteRestricoes.length > 0) {
						this.isPossuiRestricao = true;
						const index = this.clienteRestricoes.findIndex(
							(cr) => cr.tipo === TipoClienteRestricaoEnum.INADINPLENCIA
						);
						if (index >= 0) {
							this.isPossuiRestricaoPorInadimplencia = true;
						}
					}
					this.cd.detectChanges();
				});
		}
	}

	loadPermissaoApresentarMenuContrato() {
		this.admLegadoTelaClienteService
			.apresentarMenuContrato(
				this.sessionService.chave,
				Number(this.sessionService.empresaId),
				this.dadosPessoais.codigoCliente,
				{}
			)
			.subscribe((data) => {
				this.apresentarMenuContrato = data.content;
				this.cd.detectChanges();
			});
	}
}
