<div class="modal-cadastrar-controle-credito-treino">
	<form [formGroup]="form">
		<div class="mccct-row">
			<div class="mccct-info">
				<div class="mccct-info-title">Operação</div>
				<div
					*ngIf="
						controleCreditoTreino?.codigo &&
						controleCreditoTreino?.reposicao?.marcacaoAula
					"
					class="mccct-info-value">
					{{ tipoOperacaoCreditoTreinoEnum.MARCOU_AULA.nome }}
				</div>
				<div
					*ngIf="
						(!controleCreditoTreino?.codigo &&
							controleCreditoTreino.tipoOperacaoCreditoTreinoEnum) ||
						(controleCreditoTreino?.codigo &&
							!controleCreditoTreino?.reposicao?.marcacaoAula)
					"
					class="mccct-info-value">
					{{ controleCreditoTreino?.tipoOperacaoCreditoTreinoEnum?.nome }}
				</div>
			</div>
			<ng-container *ngIf="controleCreditoTreino?.codigo">
				<div class="mccct-info">
					<div class="mccct-info-title">Data operação</div>
					<div class="mccct-info-value">
						{{
							controleCreditoTreino?.dataOperacao
								| date : "dd/MM/yyyy - HH:mm:ss"
						}}
					</div>
				</div>
				<div class="mccct-info">
					<div class="mccct-info-title">Data lancamento</div>
					<div class="mccct-info-value">
						{{
							controleCreditoTreino?.dataLancamento
								| date : "dd/MM/yyyy - HH:mm:ss"
						}}
					</div>
				</div>
				<div
					*ngIf="controleCreditoTreino?.codigo && origemSistema"
					class="mccct-info">
					<div class="mccct-info-title">Sistema Utilizado</div>
					<div class="mccct-info-value">
						{{ origemSistema }}
					</div>
				</div>
				<div class="mccct-info">
					<div class="mccct-info-title">Usuario</div>
					<div class="mccct-info-value">
						{{ controleCreditoTreino?.usuario?.nome }}
					</div>
				</div>
			</ng-container>
		</div>
		<div
			*ngIf="controleCreditoTreino?.codigo && origemSistema"
			class="mccct-row">
			<div class="mccct-info mccct-info-100">
				<div class="mccct-info-title">Aula desmarcada</div>
				<div class="mccct-info-value">
					{{ aulaDesmarcada }}
				</div>
			</div>
			<div class="mccct-info mccct-info-100">
				<div class="mccct-info-title">Aula marcada</div>
				<div class="mccct-info-value">
					{{ aulaMarcada }}
				</div>
			</div>
		</div>

		<hr />
		<div style="display: flex; flex-direction: column">
			<div class="mccct-tipo-operacao">
				<div class="mccct-tipo-operacao-title">Tipo de operação</div>
				<pacto-cat-radio-group
					formControlName="codigoTipoAjusteManualCreditoTreino"
					(change)="onTipoOperacaoChange($event)">
					<pacto-cat-radio value="1">
						<label>Adicionar crédito</label>
					</pacto-cat-radio>
					<pacto-cat-radio value="2">
						<label>Retirar crédito</label>
					</pacto-cat-radio>
					<pacto-cat-radio value="3" *ngIf="mostrarTransferenciaDeCredito()">
						<label>Transferir crédito</label>
					</pacto-cat-radio>
				</pacto-cat-radio-group>
			</div>

			<div style="display: flex; gap: 16px; margin-top: 8px">
				<pacto-cat-form-input-number
					[formControl]="form.get('quantidade')"
					i18n-label="@@controle-credito-treino:quantidade"
					id="cct-novo-ipt-qtd"
					label="Quantidade"
					style="width: 276px"></pacto-cat-form-input-number>

				<pacto-cat-form-select-filter
					*ngIf="form.get('codigoTipoAjusteManualCreditoTreino')?.value === '3'"
					(search)="listClients($event)"
					[control]="form.get('clienteRecebendoTransferencia')"
					[options]="clientes"
					errorMsg="Campo obrigatório"
					i18n="@@transferir-contrato:label-cliente"
					i18n="@@transferir-contrato:cliente-obrigatorio"
					id="slct-transferir-para-cliente"
					idKey="codigo"
					label="Transferir para"
					labelKey="nome"></pacto-cat-form-select-filter>
			</div>
		</div>
		<pacto-cat-form-textarea
			[control]="form.get('observacao')"
			[disabled]="controleCreditoTreino?.codigo"
			i18n-label="@@controle-credito-treino:observacao"
			id="cct-novo-ipt-observacao"
			label="Observações"></pacto-cat-form-textarea>

		<div *ngIf="!controleCreditoTreino?.codigo" class="mccct-btn-row">
			<pacto-cat-button
				(click)="save()"
				i18n-label="@@controle-credito-treino:salvar"
				id="cct-novo-btn-save"
				label="Salvar"
				size="LARGE"></pacto-cat-button>
		</div>
	</form>
</div>
