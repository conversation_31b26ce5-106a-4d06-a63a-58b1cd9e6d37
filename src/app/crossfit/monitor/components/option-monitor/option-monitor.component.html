<pacto-title-card
	i18n-title="@@config-monitor:option-monitor:title"
	title="O que você deseja visualizar?">
	<div class="row">
		<div class="col-md-12">
			<div class="line">
				<div class="name-label">Selecionar Wod</div>
				<div class="select-workout">
					<pacto-select
						[control]="wodFormControl"
						[opcoes]="wods"></pacto-select>
				</div>
			</div>

			<div class="line">
				<span
					[ngClass]="{ 'space-label-icon': opcoesWorkout === false }"
					class="name-label"
					i18n="@@config-monitor:option-monitor:workout">
					Workout
				</span>
				<div class="selected-toggle">
					<div
						[ngClass]="{
							'nao-selecionado': estadoDasOpcoes.workout === false
						}"
						class="selected-no"
						i18n="@@config-monitor:option-monitor:toggle:nao">
						Não
					</div>
					<div class="option-toggle">
						<div class="acoes">
							<div
								(click)="toggleWorkout()"
								*ngIf="!estadoDasOpcoes.workout"
								class="toggle-box icon-inativo">
								<i class="pct pct-toggle-left" title="Ativar"></i>
							</div>
							<div
								(click)="toggleWorkout()"
								*ngIf="estadoDasOpcoes.workout"
								class="toggle-box icon-ativo">
								<i class="pct pct-toggle-right" title="Desativar"></i>
							</div>
						</div>
					</div>
					<div
						[ngClass]="{ 'sim-selecionado': estadoDasOpcoes.workout === true }"
						class="selected-yes"
						i18n="@@config-monitor:option-monitor:toggle:sim">
						Sim
					</div>
				</div>
			</div>

			<!-- RANKING START -->
			<div class="line">
				<div class="exibir">
					<span (click)="toggleDirection()">
						<i class="fa {{ sortDirection }}"></i>
						<span
							[ngClass]="{ 'space-label-icon': opcoesRanking === false }"
							class="name-label"
							i18n="@@config-monitor:option-monitor:ranking">
							Ranking
						</span>
					</span>
				</div>
				<div class="selected-toggle">
					<div
						[ngClass]="{
							'nao-selecionado': estadoDasOpcoes.ranking === false
						}"
						class="selected-no"
						i18n="@@config-monitor:option-monitor:toggle:nao">
						Não
					</div>
					<div class="option-toggle">
						<div class="acoes">
							<div
								(click)="toggleRanking()"
								*ngIf="!estadoDasOpcoes.ranking"
								class="toggle-box icon-inativo">
								<i class="pct pct-toggle-left" title="Ativar"></i>
							</div>
							<div
								(click)="toggleRanking()"
								*ngIf="estadoDasOpcoes.ranking"
								class="toggle-box icon-ativo">
								<i class="pct pct-toggle-right" title="Desativar"></i>
							</div>
						</div>
					</div>
					<div
						[ngClass]="{ 'sim-selecionado': estadoDasOpcoes.ranking === true }"
						class="selected-yes"
						i18n="@@config-monitor:option-monitor:toggle:sim">
						Sim
					</div>
				</div>
			</div>

			<div *ngIf="opcoesRanking">
				<div class="toolbar-container">
					<div
						class="toolbar-radio-button"
						i18n="@@config-monitor:option-monitor:organizacao">
						Organização
					</div>

					<div class="row">
						<div class="col-sm-6">
							<div class="form-check form-check-inline">
								<input
									(ngModelChange)="onChangeHandler()"
									[(ngModel)]="options.ranking.organizacao"
									class="form-check-input"
									id="inlineRadio1"
									name="organizacao"
									type="radio"
									value="UNISSEX" />
								<label
									class="form-check-label radio-button"
									for="inlineRadio1"
									i18n="@@config-monitor:option-monitor:unisser">
									Unissex (Padrão)
								</label>
							</div>
						</div>
					</div>
					<div class="row">
						<div class="col-sm-6">
							<div class="form-check form-check-inline">
								<input
									(ngModelChange)="onChangeHandler()"
									[(ngModel)]="options.ranking.organizacao"
									class="form-check-input"
									id="inlineRadio3"
									name="organizacao"
									type="radio"
									value="MASCULINO" />
								<label
									class="form-check-label radio-button"
									for="inlineRadio3"
									i18n="@@config-monitor:option-monitor:masculino">
									Apenas masculino
								</label>
							</div>
						</div>
					</div>
					<div class="row">
						<div class="col-lg-6">
							<div class="form-check form-check-inline">
								<input
									(ngModelChange)="onChangeHandler()"
									[(ngModel)]="options.ranking.organizacao"
									class="form-check-input"
									id="inlineRadio5"
									name="organizacao"
									type="radio"
									value="FEMININO" />
								<label
									class="form-check-label radio-button"
									for="inlineRadio5"
									i18n="@@config-monitor:option-monitor:feminino">
									Apenas feminino
								</label>
							</div>
						</div>
					</div>
				</div>
			</div>
			<!-- RANKING END -->

			<div class="line">
				<div
					class="name-label"
					i18n="@@config-monitor:option-monitor:resultados">
					Resultados
				</div>
				<div class="selected-toggle">
					<div
						[ngClass]="{
							'nao-selecionado': estadoDasOpcoes.resultados === false
						}"
						class="selected-no"
						i18n="@@config-monitor:option-monitor:toggle:nao">
						Não
					</div>
					<div class="option-toggle">
						<div class="acoes">
							<div
								(click)="toggleResultados()"
								*ngIf="!estadoDasOpcoes.resultados"
								class="toggle-box icon-inativo">
								<i class="pct pct-toggle-left" title="Ativar"></i>
							</div>
							<div
								(click)="toggleResultados()"
								*ngIf="estadoDasOpcoes.resultados"
								class="toggle-box icon-ativo">
								<i class="pct pct-toggle-right" title="Desativar"></i>
							</div>
						</div>
					</div>
					<div
						[ngClass]="{
							'sim-selecionado': estadoDasOpcoes.resultados === true
						}"
						class="selected-yes"
						i18n="@@config-monitor:option-monitor:toggle:sim">
						Sim
					</div>
				</div>
			</div>

			<div class="line">
				<div
					class="name-label"
					i18n="@@config-monitor:option-monitor:visitantes">
					Visitantes em destaque
				</div>
				<div class="selected-toggle">
					<div
						[ngClass]="{
							'nao-selecionado': estadoDasOpcoes.visitantes === false
						}"
						class="selected-no"
						i18n="@@config-monitor:option-monitor:toggle:nao">
						Não
					</div>
					<div class="option-toggle">
						<div class="acoes">
							<div
								(click)="toggleVisitantes()"
								*ngIf="!estadoDasOpcoes.visitantes"
								class="toggle-box icon-inativo">
								<i class="pct pct-toggle-left" title="Ativar"></i>
							</div>
							<div
								(click)="toggleVisitantes()"
								*ngIf="estadoDasOpcoes.visitantes"
								class="toggle-box icon-ativo">
								<i class="pct pct-toggle-right" title="Desativar"></i>
							</div>
						</div>
					</div>
					<div
						[ngClass]="{
							'sim-selecionado': estadoDasOpcoes.visitantes === true
						}"
						class="selected-yes"
						i18n="@@config-monitor:option-monitor:toggle:sim">
						Sim
					</div>
				</div>
			</div>

			<div class="line">
				<div class="name-label">Tamanho da fonte</div>
				<div class="select-workout">
					<pacto-select
						[control]="fontesControl"
						[opcoes]="fontes"></pacto-select>
				</div>
			</div>
		</div>

		<div class="actions-monitor">
			<button
				(click)="clickHandler()"
				[disabled]="wods.length === 0"
				class="btn btn-primary">
				Gerar link
			</button>
		</div>
	</div>
</pacto-title-card>

<span
	#mensagemSelecioneItem
	[hidden]="true"
	i18n="@@config-monitor:option-monitor:mensagem-validacao">
	Para prosseguir, selecione os itens que serão mostrados no monitor
</span>

<span #pequena [hidden]="true" i18n="@@monitor:pequena">Pequena</span>
<span #media [hidden]="true" i18n="@@monitor:media">Média</span>
<span #grande [hidden]="true" i18n="@@monitor:grande">Grande</span>
<span #muitoGrande [hidden]="true" i18n="@@monitor:muitoGrande">
	Muito grande
</span>
<span #gigante [hidden]="true" i18n="@@monitor:gigante">Gigante</span>
